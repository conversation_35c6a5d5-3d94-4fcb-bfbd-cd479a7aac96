{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\admin\\\\Messages.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Messages = ({\n  user\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedMessage, setSelectedMessage] = useState(null);\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setMessages([{\n        id: 1,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        subject: 'Product Inquiry',\n        message: 'Hello, I would like to know more about the anime figures you have in stock.',\n        created_at: '2024-01-15',\n        read: false\n      }, {\n        id: 2,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        subject: 'Shipping Question',\n        message: 'When will my order be shipped? I placed it 3 days ago.',\n        created_at: '2024-01-14',\n        read: true\n      }, {\n        id: 3,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        subject: 'Return Request',\n        message: 'I received a damaged item and would like to return it.',\n        created_at: '2024-01-13',\n        read: false\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const markAsRead = messageId => {\n    setMessages(messages.map(msg => msg.id === messageId ? {\n      ...msg,\n      read: true\n    } : msg));\n  };\n  const deleteMessage = messageId => {\n    setMessages(messages.filter(msg => msg.id !== messageId));\n    setSelectedMessage(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      user: user,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Messages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"All Messages\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"Unread\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"Read\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-200\",\n              children: messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => {\n                  setSelectedMessage(message);\n                  if (!message.read) markAsRead(message.id);\n                },\n                className: `p-4 cursor-pointer hover:bg-gray-50 ${!message.read ? 'bg-blue-50 border-l-4 border-blue-500' : ''} ${(selectedMessage === null || selectedMessage === void 0 ? void 0 : selectedMessage.id) === message.id ? 'bg-red-50' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: message.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: message.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 99,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700 mt-1 font-medium\",\n                      children: message.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: message.created_at\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this), !message.read && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this)\n              }, message.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: selectedMessage ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-900\",\n                    children: selectedMessage.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 mt-1\",\n                    children: [\"From: \", selectedMessage.name, \" (\", selectedMessage.email, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: selectedMessage.created_at\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteMessage(selectedMessage.id),\n                    className: \"text-red-600 hover:text-red-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 132,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 whitespace-pre-wrap\",\n                children: selectedMessage.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 mr-3\",\n                children: \"Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"text-gray-600 hover:text-gray-700\",\n                children: \"Forward\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-12 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"mx-auto h-12 w-12 text-gray-400 mb-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"Select a message to view details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-blue-100 text-blue-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Messages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: messages.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-yellow-100 text-yellow-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Unread\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: messages.filter(m => !m.read).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-green-100 text-green-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Read\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: messages.filter(m => m.read).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(Messages, \"YDxalipMmNv6P76ms2s36Zc5vAQ=\");\n_c = Messages;\nexport default Messages;\nvar _c;\n$RefreshReg$(_c, \"Messages\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminLayout", "jsxDEV", "_jsxDEV", "Messages", "user", "_s", "messages", "setMessages", "loading", "setLoading", "selectedMessage", "setSelectedMessage", "setTimeout", "id", "name", "email", "subject", "message", "created_at", "read", "mark<PERSON><PERSON><PERSON>", "messageId", "map", "msg", "deleteMessage", "filter", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "m", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/admin/Messages.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\n\nconst Messages = ({ user }) => {\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedMessage, setSelectedMessage] = useState(null);\n\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setMessages([\n        { \n          id: 1, \n          name: '<PERSON>', \n          email: '<EMAIL>', \n          subject: 'Product Inquiry', \n          message: 'Hello, I would like to know more about the anime figures you have in stock.',\n          created_at: '2024-01-15',\n          read: false\n        },\n        { \n          id: 2, \n          name: '<PERSON>', \n          email: '<EMAIL>', \n          subject: 'Shipping Question', \n          message: 'When will my order be shipped? I placed it 3 days ago.',\n          created_at: '2024-01-14',\n          read: true\n        },\n        { \n          id: 3, \n          name: '<PERSON>', \n          email: '<EMAIL>', \n          subject: 'Return Request', \n          message: 'I received a damaged item and would like to return it.',\n          created_at: '2024-01-13',\n          read: false\n        },\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const markAsRead = (messageId) => {\n    setMessages(messages.map(msg => \n      msg.id === messageId ? { ...msg, read: true } : msg\n    ));\n  };\n\n  const deleteMessage = (messageId) => {\n    setMessages(messages.filter(msg => msg.id !== messageId));\n    setSelectedMessage(null);\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout user={user}>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout user={user}>\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Messages</h1>\n          <div className=\"flex space-x-2\">\n            <select className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\">\n              <option>All Messages</option>\n              <option>Unread</option>\n              <option>Read</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Messages List */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"divide-y divide-gray-200\">\n                {messages.map((message) => (\n                  <div\n                    key={message.id}\n                    onClick={() => {\n                      setSelectedMessage(message);\n                      if (!message.read) markAsRead(message.id);\n                    }}\n                    className={`p-4 cursor-pointer hover:bg-gray-50 ${\n                      !message.read ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                    } ${selectedMessage?.id === message.id ? 'bg-red-50' : ''}`}\n                  >\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">{message.name}</p>\n                        <p className=\"text-xs text-gray-500\">{message.email}</p>\n                        <p className=\"text-sm text-gray-700 mt-1 font-medium\">{message.subject}</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">{message.created_at}</p>\n                      </div>\n                      {!message.read && (\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Message Detail */}\n          <div className=\"lg:col-span-2\">\n            {selectedMessage ? (\n              <div className=\"bg-white rounded-lg shadow-md\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <h2 className=\"text-xl font-bold text-gray-900\">{selectedMessage.subject}</h2>\n                      <p className=\"text-sm text-gray-600 mt-1\">\n                        From: {selectedMessage.name} ({selectedMessage.email})\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">{selectedMessage.created_at}</p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => deleteMessage(selectedMessage.id)}\n                        className=\"text-red-600 hover:text-red-700\"\n                      >\n                        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <p className=\"text-gray-700 whitespace-pre-wrap\">{selectedMessage.message}</p>\n                </div>\n                <div className=\"p-6 border-t border-gray-200\">\n                  <button className=\"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 mr-3\">\n                    Reply\n                  </button>\n                  <button className=\"text-gray-600 hover:text-gray-700\">\n                    Forward\n                  </button>\n                </div>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow-md p-12 text-center\">\n                <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-gray-500\">Select a message to view details</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-blue-100 text-blue-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Messages</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{messages.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-yellow-100 text-yellow-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Unread</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{messages.filter(m => !m.read).length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-green-100 text-green-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Read</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{messages.filter(m => m.read).length}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Messages;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACd;IACAa,UAAU,CAAC,MAAM;MACfL,WAAW,CAAC,CACV;QACEM,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE,iBAAiB;QAC1BC,OAAO,EAAE,6EAA6E;QACtFC,UAAU,EAAE,YAAY;QACxBC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE,mBAAmB;QAC5BC,OAAO,EAAE,wDAAwD;QACjEC,UAAU,EAAE,YAAY;QACxBC,IAAI,EAAE;MACR,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE,gBAAgB;QACzBC,OAAO,EAAE,wDAAwD;QACjEC,UAAU,EAAE,YAAY;QACxBC,IAAI,EAAE;MACR,CAAC,CACF,CAAC;MACFV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,UAAU,GAAIC,SAAS,IAAK;IAChCd,WAAW,CAACD,QAAQ,CAACgB,GAAG,CAACC,GAAG,IAC1BA,GAAG,CAACV,EAAE,KAAKQ,SAAS,GAAG;MAAE,GAAGE,GAAG;MAAEJ,IAAI,EAAE;IAAK,CAAC,GAAGI,GAClD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,aAAa,GAAIH,SAAS,IAAK;IACnCd,WAAW,CAACD,QAAQ,CAACmB,MAAM,CAACF,GAAG,IAAIA,GAAG,CAACV,EAAE,KAAKQ,SAAS,CAAC,CAAC;IACzDV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACEN,OAAA,CAACF,WAAW;MAACI,IAAI,EAAEA,IAAK;MAAAsB,QAAA,eACtBxB,OAAA;QAAKyB,SAAS,EAAC,gDAAgD;QAAAD,QAAA,eAC7DxB,OAAA;UAAKyB,SAAS,EAAC;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;EAEA,oBACE7B,OAAA,CAACF,WAAW;IAACI,IAAI,EAAEA,IAAK;IAAAsB,QAAA,eACtBxB,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBxB,OAAA;QAAKyB,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDxB,OAAA;UAAIyB,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D7B,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAD,QAAA,eAC7BxB,OAAA;YAAQyB,SAAS,EAAC,gGAAgG;YAAAD,QAAA,gBAChHxB,OAAA;cAAAwB,QAAA,EAAQ;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B7B,OAAA;cAAAwB,QAAA,EAAQ;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvB7B,OAAA;cAAAwB,QAAA,EAAQ;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7B,OAAA;QAAKyB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBAEpDxB,OAAA;UAAKyB,SAAS,EAAC,eAAe;UAAAD,QAAA,eAC5BxB,OAAA;YAAKyB,SAAS,EAAC,+CAA+C;YAAAD,QAAA,eAC5DxB,OAAA;cAAKyB,SAAS,EAAC,0BAA0B;cAAAD,QAAA,EACtCpB,QAAQ,CAACgB,GAAG,CAAEL,OAAO,iBACpBf,OAAA;gBAEE8B,OAAO,EAAEA,CAAA,KAAM;kBACbrB,kBAAkB,CAACM,OAAO,CAAC;kBAC3B,IAAI,CAACA,OAAO,CAACE,IAAI,EAAEC,UAAU,CAACH,OAAO,CAACJ,EAAE,CAAC;gBAC3C,CAAE;gBACFc,SAAS,EAAE,uCACT,CAACV,OAAO,CAACE,IAAI,GAAG,uCAAuC,GAAG,EAAE,IAC1D,CAAAT,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,EAAE,MAAKI,OAAO,CAACJ,EAAE,GAAG,WAAW,GAAG,EAAE,EAAG;gBAAAa,QAAA,eAE5DxB,OAAA;kBAAKyB,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,gBAC/CxB,OAAA;oBAAKyB,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrBxB,OAAA;sBAAGyB,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAAET,OAAO,CAACH;oBAAI;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnE7B,OAAA;sBAAGyB,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAET,OAAO,CAACF;oBAAK;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxD7B,OAAA;sBAAGyB,SAAS,EAAC,wCAAwC;sBAAAD,QAAA,EAAET,OAAO,CAACD;oBAAO;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3E7B,OAAA;sBAAGyB,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAET,OAAO,CAACC;oBAAU;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EACL,CAACd,OAAO,CAACE,IAAI,iBACZjB,OAAA;oBAAKyB,SAAS,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACxD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC,GAnBDd,OAAO,CAACJ,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBZ,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7B,OAAA;UAAKyB,SAAS,EAAC,eAAe;UAAAD,QAAA,EAC3BhB,eAAe,gBACdR,OAAA;YAAKyB,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC5CxB,OAAA;cAAKyB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3CxB,OAAA;gBAAKyB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,gBAC/CxB,OAAA;kBAAAwB,QAAA,gBACExB,OAAA;oBAAIyB,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,EAAEhB,eAAe,CAACM;kBAAO;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9E7B,OAAA;oBAAGyB,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,GAAC,QAClC,EAAChB,eAAe,CAACI,IAAI,EAAC,IAAE,EAACJ,eAAe,CAACK,KAAK,EAAC,GACvD;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJ7B,OAAA;oBAAGyB,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EAAEhB,eAAe,CAACQ;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACN7B,OAAA;kBAAKyB,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,eAC7BxB,OAAA;oBACE8B,OAAO,EAAEA,CAAA,KAAMR,aAAa,CAACd,eAAe,CAACG,EAAE,CAAE;oBACjDc,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,eAE3CxB,OAAA;sBAAKyB,SAAS,EAAC,SAAS;sBAACM,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAT,QAAA,eAC5ExB,OAAA;wBAAMkC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACC,CAAC,EAAC;sBAA8H;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAKyB,SAAS,EAAC,KAAK;cAAAD,QAAA,eAClBxB,OAAA;gBAAGyB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAEhB,eAAe,CAACO;cAAO;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACN7B,OAAA;cAAKyB,SAAS,EAAC,8BAA8B;cAAAD,QAAA,gBAC3CxB,OAAA;gBAAQyB,SAAS,EAAC,kEAAkE;gBAAAD,QAAA,EAAC;cAErF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7B,OAAA;gBAAQyB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN7B,OAAA;YAAKyB,SAAS,EAAC,gDAAgD;YAAAD,QAAA,gBAC7DxB,OAAA;cAAKyB,SAAS,EAAC,sCAAsC;cAACM,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAT,QAAA,eACzGxB,OAAA;gBAAMkC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAsG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K,CAAC,eACN7B,OAAA;cAAGyB,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKyB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpDxB,OAAA;UAAKyB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDxB,OAAA;YAAKyB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCxB,OAAA;cAAKyB,SAAS,EAAC,4CAA4C;cAAAD,QAAA,eACzDxB,OAAA;gBAAKyB,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5ExB,OAAA;kBAAMkC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAKyB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxB,OAAA;gBAAGyB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnE7B,OAAA;gBAAGyB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEpB,QAAQ,CAACkC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKyB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDxB,OAAA;YAAKyB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCxB,OAAA;cAAKyB,SAAS,EAAC,gDAAgD;cAAAD,QAAA,eAC7DxB,OAAA;gBAAKyB,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5ExB,OAAA;kBAAMkC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAmD;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAKyB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxB,OAAA;gBAAGyB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3D7B,OAAA;gBAAGyB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEpB,QAAQ,CAACmB,MAAM,CAACgB,CAAC,IAAI,CAACA,CAAC,CAACtB,IAAI,CAAC,CAACqB;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKyB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDxB,OAAA;YAAKyB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCxB,OAAA;cAAKyB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,eAC3DxB,OAAA;gBAAKyB,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5ExB,OAAA;kBAAMkC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA+C;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAKyB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxB,OAAA;gBAAGyB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzD7B,OAAA;gBAAGyB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEpB,QAAQ,CAACmB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACtB,IAAI,CAAC,CAACqB;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAC1B,EAAA,CA5MIF,QAAQ;AAAAuC,EAAA,GAARvC,QAAQ;AA8Md,eAAeA,QAAQ;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}