<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
include_once(__DIR__ . '/../includes/store/header.php');
$title = "Cart";

$cartItems = [];
$subtotal = 0;
$shipping = 5.99;
$taxRate = 0.05;

if (isset($_SESSION['user'])) {
    $userId = $_SESSION['user']['id'];
    $cartQuery = mysqli_query($mysqli, "SELECT oi.*, p.name, p.price, p.category_id, (SELECT image_path FROM ProductImages WHERE product_id = p.id LIMIT 1) as img FROM OrderItems oi JOIN Products p ON oi.product_id = p.id JOIN Orders o ON oi.order_id = o.id WHERE o.user_id = $userId AND o.status = 'cart'");
    while ($row = mysqli_fetch_assoc($cartQuery)) {
        $cartItems[] = $row;
        $subtotal += $row['price'] * $row['quantity'];
    }
} else {
    // Guest: cart will be loaded by JS from localStorage
}
$tax = $subtotal * $taxRate;
$total = $subtotal + $shipping + $tax;
?>

<section class="container py-10">
    <div class="flex flex-col space-y-8">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold">Shopping Cart</h1>
            <p class="text-gray-600 mt-2">Review and manage your items</p>
        </div>

        <?php if (empty($cartItems)): ?>
            <div class="py-10 text-center">
                <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <h2 class="mt-4 text-lg font-medium">Your cart is empty</h2>
                <p class="mt-2 text-gray-500">Add some items to your cart to continue shopping</p>
                <a href="/products.php"
                    class="mt-4 inline-block bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition">
                    Browse Products
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 rounded-lg">
                    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-red-50">
                        <div class="p-6 border-b border-gray-300">
                            <h2 class="text-xl font-bold">Cart Items (<?= count($cartItems) ?>)</h2>
                        </div>

                        <div class="divide-y divide-gray-300">
                            <?php foreach ($cartItems as $item): ?>
                                <div class="p-6 flex flex-col sm:flex-row">
                                    <div class="sm:mr-6 mb-4 sm:mb-0">
                                        <img src="<?= $item['img'] ?>" alt="<?= htmlspecialchars($item['name']) ?>"
                                            class="w-24 h-24 object-cover rounded-md">
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex flex-col sm:flex-row sm:justify-between">
                                            <div>
                                                <h3 class="font-bold text-gray-800"><?= htmlspecialchars($item['name']) ?></h3>
                                                <p class="text-sm text-gray-500"><?= htmlspecialchars($item['category']) ?></p>
                                            </div>
                                            <p class="font-bold text-gray-800 mt-2 sm:mt-0">
                                                $<?= number_format($item['price'] * $item['quantity'], 2) ?>
                                            </p>
                                        </div>
                                        <div class="mt-4 flex flex-wrap justify-between items-center">
                                            <div class="flex items-center border rounded-md overflow-hidden">
                                                <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 transition"
                                                    onclick="updateQuantity(<?= $item['id'] ?>, 'decrease')">-</button>
                                                <input type="text" value="<?= $item['quantity'] ?>"
                                                    class="w-12 text-center border-0 focus:ring-0" readonly>
                                                <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 transition"
                                                    onclick="updateQuantity(<?= $item['id'] ?>, 'increase')">+</button>
                                            </div>
                                            <button
                                                class="text-red-600 hover:text-red-800 text-sm flex items-center mt-2 sm:mt-0"
                                                onclick="removeItem(<?= $item['id'] ?>)">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                    </path>
                                                </svg>
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-between items-center">
                        <a href="/products.php" class="flex items-center text-red-600 hover:text-red-800">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Continue Shopping
                        </a>
                        <button class="text-red-600 hover:text-red-800" onclick="clearCart()">
                            Clear Cart
                        </button>
                    </div>
                </div>

                <div class="lg:col-span-1 rounded-lg">
                    <div class="bg-white rounded-lg shadow-md border border-red-50 p-6 sticky top-25">
                        <h2 class="text-xl font-bold mb-4">Order Summary</h2>

                        <div class="space-y-3 text-sm mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span>$<?= number_format($subtotal, 2) ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span>$<?= number_format($shipping, 2) ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax (5%)</span>
                                <span>$<?= number_format($tax, 2) ?></span>
                            </div>
                            <div class="border-t pt-3 mt-3 flex justify-between font-bold">
                                <span>Total</span>
                                <span>$<?= number_format($total, 2) ?></span>
                            </div>
                        </div>

                        <div class="mt-6">
                            <a href="/checkout.php"
                                class="block text-center bg-red-600 text-white py-3 px-6 rounded-md hover:bg-red-700 transition w-full font-bold">
                                Proceed to Checkout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php if (!isset($_SESSION['user'])): ?>
<script>
// Guest cart: load from localStorage
function renderGuestCart() {
    var cart = JSON.parse(localStorage.getItem('cart') || '[]');
    var cartContainer = document.querySelector('.grid.grid-cols-1');
    var summaryContainer = document.querySelector('.lg\:col-span-1 .bg-white');
    var subtotal = 0;
    var shipping = 5.99;
    var taxRate = 0.05;
    var html = '';
    if (cart.length === 0) {
        cartContainer.innerHTML = `<div class='py-10 text-center'><h2 class='mt-4 text-lg font-medium'>Your cart is empty</h2><a href='/products.php' class='mt-4 inline-block bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition'>Browse Products</a></div>`;
        summaryContainer.innerHTML = '';
        return;
    }
    html += `<div class='bg-white rounded-lg shadow-md overflow-hidden border border-red-50'><div class='p-6 border-b border-gray-300'><h2 class='text-xl font-bold'>Cart Items (${cart.length})</h2></div><div class='divide-y divide-gray-300'>`;
    cart.forEach(function(item, idx) {
        subtotal += (item.price || 0) * (item.quantity || 1);
        html += `<div class='p-6 flex flex-col sm:flex-row'><div class='sm:mr-6 mb-4 sm:mb-0'><img src='${item.img || 'assets/images/Kyo2.jpg'}' alt='${item.name || ''}' class='w-24 h-24 object-cover rounded-md'></div><div class='flex-1'><div class='flex flex-col sm:flex-row sm:justify-between'><div><h3 class='font-bold text-gray-800'>${item.name || ''}</h3><p class='text-sm text-gray-500'>${item.category || ''}</p></div><p class='font-bold text-gray-800 mt-2 sm:mt-0'>$${((item.price || 0) * (item.quantity || 1)).toFixed(2)}</p></div><div class='mt-4 flex flex-wrap justify-between items-center'><div class='flex items-center border rounded-md overflow-hidden'><button class='px-3 py-1 bg-gray-100 hover:bg-gray-200 transition' onclick='updateQuantity(${idx}, "decrease")'>-</button><input type='text' value='${item.quantity || 1}' class='w-12 text-center border-0 focus:ring-0' readonly><button class='px-3 py-1 bg-gray-100 hover:bg-gray-200 transition' onclick='updateQuantity(${idx}, "increase")'>+</button></div><button class='text-red-600 hover:text-red-800 text-sm flex items-center mt-2 sm:mt-0' onclick='removeItem(${idx})'><svg class='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'></path></svg>Remove</button></div></div></div>`;
    });
    html += `</div></div><div class='mt-6 flex justify-between items-center'><a href='/products.php' class='flex items-center text-red-600 hover:text-red-800'><svg class='w-5 h-5 mr-2' fill='none' stroke='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 19l-7-7m0 0l7-7m-7 7h18'></path></svg>Continue Shopping</a><button class='text-red-600 hover:text-red-800' onclick='clearCart()'>Clear Cart</button></div>`;
    cartContainer.innerHTML = html;
    var tax = subtotal * taxRate;
    var total = subtotal + shipping + tax;
    summaryContainer.innerHTML = `<h2 class='text-xl font-bold mb-4'>Order Summary</h2><div class='space-y-3 text-sm mb-6'><div class='flex justify-between'><span class='text-gray-600'>Subtotal</span><span>$${subtotal.toFixed(2)}</span></div><div class='flex justify-between'><span class='text-gray-600'>Shipping</span><span>$${shipping.toFixed(2)}</span></div><div class='flex justify-between'><span class='text-gray-600'>Tax (5%)</span><span>$${tax.toFixed(2)}</span></div><div class='border-t pt-3 mt-3 flex justify-between font-bold'><span>Total</span><span>$${total.toFixed(2)}</span></div></div><div class='mt-6'><a href='/checkout.php' class='block text-center bg-red-600 text-white py-3 px-6 rounded-md hover:bg-red-700 transition w-full font-bold'>Proceed to Checkout</a></div>`;
}
function updateQuantity(idx, action) {
    var cart = JSON.parse(localStorage.getItem('cart') || '[]');
    if (action === 'increase') cart[idx].quantity = (cart[idx].quantity || 1) + 1;
    if (action === 'decrease' && cart[idx].quantity > 1) cart[idx].quantity--;
    localStorage.setItem('cart', JSON.stringify(cart));
    renderGuestCart();
    document.getElementById('cart-count').textContent = cart.reduce((sum, item) => sum + (item.quantity || 1), 0);
}
function removeItem(idx) {
    var cart = JSON.parse(localStorage.getItem('cart') || '[]');
    cart.splice(idx, 1);
    localStorage.setItem('cart', JSON.stringify(cart));
    renderGuestCart();
    document.getElementById('cart-count').textContent = cart.reduce((sum, item) => sum + (item.quantity || 1), 0);
}
function clearCart() {
    localStorage.removeItem('cart');
    renderGuestCart();
    document.getElementById('cart-count').textContent = 0;
}
document.addEventListener('DOMContentLoaded', renderGuestCart);
</script>
<?php endif; ?>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>