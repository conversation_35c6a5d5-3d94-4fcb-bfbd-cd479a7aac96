RewriteEngine On

# Handle CORS preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API Routes
RewriteRule ^api/auth/(.*)$ api/auth.php [QSA,L]
RewriteRule ^api/products/(.*)$ api/products.php [QSA,L]
RewriteRule ^api/categories/(.*)$ api/categories.php [QSA,L]
RewriteRule ^api/cart/(.*)$ api/cart.php [QSA,L]
RewriteRule ^api/orders/(.*)$ api/orders.php [QSA,L]
RewriteRule ^api/users/(.*)$ api/users.php [QSA,L]
RewriteRule ^api/contact/(.*)$ api/contact.php [QSA,L]
RewriteRule ^api/settings/(.*)$ api/settings.php [QSA,L]

# Fallback for other API routes
RewriteRule ^api/(.*)$ api/index.php [QSA,L]
