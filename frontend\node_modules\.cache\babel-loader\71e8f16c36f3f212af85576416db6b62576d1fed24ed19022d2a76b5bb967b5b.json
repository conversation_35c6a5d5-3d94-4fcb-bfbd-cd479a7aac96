{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  user,\n  cartCount = 0,\n  onLogout\n}) => {\n  _s();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);\n  const location = useLocation();\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"sticky top-0 z-50 w-full shadow-md bg-white backdrop-blur supports-[backdrop-filter]:bg-white/80\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/website/logo.png\",\n              alt: \"KYOPAL\",\n              className: \"w-10 h-10 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"inline-block text-xl font-bold text-black\",\n              children: \"KYOPAL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden lg:flex space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: `text-sm font-medium transition-colors ${isActive('/') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'}`,\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: `text-sm font-medium transition-colors ${isActive('/products') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'}`,\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: `text-sm font-medium transition-colors ${isActive('/about') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'}`,\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: `text-sm font-medium transition-colors ${isActive('/contact') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'}`,\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative hidden sm:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search...\",\n              className: \"w-64 px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-gray-400\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileSearchOpen(!isMobileSearchOpen),\n            className: \"sm:hidden text-gray-600 hover:text-red-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/cart\",\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6 text-gray-600 group-hover:text-red-600 transition-colors\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center group-hover:bg-red-700 transition-colors\",\n              children: cartCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsProfileOpen(!isProfileOpen),\n              className: \"text-gray-600 flex items-center hover:text-red-600 transition-colors focus:outline-none cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `h-3 w-3 transition-transform duration-300 ${isProfileOpen ? 'rotate-180' : ''}`,\n                fill: \"currentColor\",\n                viewBox: \"0 0 330 330\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393  c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393  s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), isProfileOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg py-2 z-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm leading-5 font-medium text-gray-900\",\n                  children: \"User Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs leading-4 font-normal text-gray-500 mt-1\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: \"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-3 text-gray-400\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 25\n                  }, this), \"Profile\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/orders\",\n                className: \"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-3 text-gray-400\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 25\n                  }, this), \"My Orders\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-100 my-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: onLogout,\n                className: \"w-full text-left block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-3 text-gray-400\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 25\n                  }, this), \"Logout\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"lg:hidden text-gray-600 hover:text-red-600 rounded-full cursor-pointer transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:hidden border-t border-gray-200 rounded-b-xl\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex flex-col divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: `group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              className: \"w-5 h-5 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: `group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/products') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              className: \"w-5 h-5 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"m7.5 4.27 9 5.15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"m3.3 7 8.7 5 8.7-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 22V12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: `group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/about') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              className: \"w-5 h-5 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 16v-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 8h.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: `group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/contact') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              className: \"w-5 h-5 transition duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), !user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2 py-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-center text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"text-center text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 9\n    }, this), isMobileSearchOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:hidden border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mx-auto px-4 py-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search...\",\n            className: \"w-full px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"bTO8AE0cyKzr86bfQElBtdNPKi8=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Header", "user", "cartCount", "onLogout", "_s", "isProfileOpen", "setIsProfileOpen", "isMobileMenuOpen", "setIsMobileMenuOpen", "isMobileSearchOpen", "setIsMobileSearchOpen", "location", "isActive", "path", "pathname", "className", "children", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "email", "width", "height", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/components/Header.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Header = ({ user, cartCount = 0, onLogout }) => {\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);\n  const location = useLocation();\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full shadow-md bg-white backdrop-blur supports-[backdrop-filter]:bg-white/80\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\n              <img src=\"/images/website/logo.png\" alt=\"KYOPAL\" className=\"w-10 h-10 rounded-full\" />\n              <span className=\"inline-block text-xl font-bold text-black\">KYOPAL</span>\n            </Link>\n          </div>\n\n          <nav className=\"hidden lg:flex space-x-6\">\n            <Link \n              to=\"/\" \n              className={`text-sm font-medium transition-colors ${\n                isActive('/') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'\n              }`}\n            >\n              Home\n            </Link>\n            <Link \n              to=\"/products\" \n              className={`text-sm font-medium transition-colors ${\n                isActive('/products') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'\n              }`}\n            >\n              Products\n            </Link>\n            <Link \n              to=\"/about\" \n              className={`text-sm font-medium transition-colors ${\n                isActive('/about') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'\n              }`}\n            >\n              About\n            </Link>\n            <Link \n              to=\"/contact\" \n              className={`text-sm font-medium transition-colors ${\n                isActive('/contact') ? 'text-red-600 hover:text-red-800' : 'text-gray-600 hover:text-red-600'\n              }`}\n            >\n              Contact\n            </Link>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Search Bar */}\n            <div className=\"relative hidden sm:block\">\n              <input \n                type=\"text\" \n                placeholder=\"Search...\"\n                className=\"w-64 px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300\" \n              />\n              <button className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </button>\n            </div>\n\n            {/* Mobile Search Button */}\n            <button \n              onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}\n              className=\"sm:hidden text-gray-600 hover:text-red-600 transition-colors\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </button>\n\n            {/* Cart */}\n            <Link to=\"/cart\" className=\"relative group\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-gray-600 group-hover:text-red-600 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n              <span className=\"absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center group-hover:bg-red-700 transition-colors\">\n                {cartCount}\n              </span>\n            </Link>\n\n            {/* Auth Buttons / Profile */}\n            {!user ? (\n              <div className=\"hidden sm:flex items-center space-x-2\">\n                <Link to=\"/login\" className=\"text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50\">\n                  Login\n                </Link>\n                <Link to=\"/register\" className=\"text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md\">\n                  Sign Up\n                </Link>\n              </div>\n            ) : (\n              <div className=\"relative\">\n                <button \n                  onClick={() => setIsProfileOpen(!isProfileOpen)}\n                  className=\"text-gray-600 flex items-center hover:text-red-600 transition-colors focus:outline-none cursor-pointer\"\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                  <svg className={`h-3 w-3 transition-transform duration-300 ${isProfileOpen ? 'rotate-180' : ''}`} fill=\"currentColor\" viewBox=\"0 0 330 330\">\n                    <path d=\"M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393  c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393  s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z\" />\n                  </svg>\n                </button>\n\n                {isProfileOpen && (\n                  <div className=\"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg py-2 z-50\">\n                    <div className=\"px-4 py-3 border-b border-gray-100\">\n                      <p className=\"text-sm leading-5 font-medium text-gray-900\">User Account</p>\n                      <p className=\"text-xs leading-4 font-normal text-gray-500 mt-1\">{user.email}</p>\n                    </div>\n                    <Link to=\"/account\" className=\"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\">\n                      <div className=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                        Profile\n                      </div>\n                    </Link>\n                    <Link to=\"/orders\" className=\"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\">\n                      <div className=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                        </svg>\n                        My Orders\n                      </div>\n                    </Link>\n                    <div className=\"border-t border-gray-100 my-1\"></div>\n                    <button \n                      onClick={onLogout}\n                      className=\"w-full text-left block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors\"\n                    >\n                      <div className=\"flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        Logout\n                      </div>\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <button \n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden text-gray-600 hover:text-red-600 rounded-full cursor-pointer transition-colors\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"lg:hidden border-t border-gray-200 rounded-b-xl\">\n          <div className=\"px-6\">\n            <nav className=\"flex flex-col divide-y divide-gray-200\">\n              <Link to=\"/\" className={`group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`}>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"w-5 h-5 transition duration-300\">\n                  <path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\" />\n                  <path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\" />\n                </svg>\n                <span>Home</span>\n              </Link>\n              <Link to=\"/products\" className={`group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/products') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`}>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"w-5 h-5 transition duration-300\">\n                  <path d=\"m7.5 4.27 9 5.15\"></path>\n                  <path d=\"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\"></path>\n                  <path d=\"m3.3 7 8.7 5 8.7-5\"></path>\n                  <path d=\"M12 22V12\"></path>\n                </svg>\n                <span>Products</span>\n              </Link>\n              <Link to=\"/about\" className={`group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/about') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`}>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"w-5 h-5 transition duration-300\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <path d=\"M12 16v-4\"></path>\n                  <path d=\"M12 8h.01\"></path>\n                </svg>\n                <span>About</span>\n              </Link>\n              <Link to=\"/contact\" className={`group flex items-center gap-4 py-4 text-md font-semibold transition-all duration-300 ${isActive('/contact') ? 'text-red-600 hover:text-red-800' : 'text-gray-700 hover:text-red-600'}`}>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"w-5 h-5 transition duration-300\">\n                  <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"></path>\n                </svg>\n                <span>Contact</span>\n              </Link>\n              {!user && (\n                <div className=\"flex flex-col gap-2 py-4\">\n                  <Link to=\"/login\" className=\"text-center text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50\">\n                    Login\n                  </Link>\n                  <Link to=\"/register\" className=\"text-center text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md\">\n                    Sign Up\n                  </Link>\n                </div>\n              )}\n            </nav>\n          </div>\n        </div>\n      )}\n\n      {/* Mobile Search */}\n      {isMobileSearchOpen && (\n        <div className=\"sm:hidden border-t border-gray-200\">\n          <div className=\"mx-auto px-4 py-3\">\n            <div className=\"relative\">\n              <input \n                type=\"text\" \n                placeholder=\"Search...\"\n                className=\"w-full px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300\" \n              />\n              <button className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS,GAAG,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAMiB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,QAAQ,GAAIC,IAAI,IAAKF,QAAQ,CAACG,QAAQ,KAAKD,IAAI;EAErD,oBACEd,OAAA;IAAQgB,SAAS,EAAC,kGAAkG;IAAAC,QAAA,gBAClHjB,OAAA;MAAKgB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCjB,OAAA;QAAKgB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjB,OAAA;UAAKgB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAClDjB,OAAA;cAAKmB,GAAG,EAAC,0BAA0B;cAACC,GAAG,EAAC,QAAQ;cAACJ,SAAS,EAAC;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtFxB,OAAA;cAAMgB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENxB,OAAA;UAAKgB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCjB,OAAA,CAACH,IAAI;YACHqB,EAAE,EAAC,GAAG;YACNF,SAAS,EAAE,yCACTH,QAAQ,CAAC,GAAG,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EACrF;YAAAI,QAAA,EACJ;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;YACHqB,EAAE,EAAC,WAAW;YACdF,SAAS,EAAE,yCACTH,QAAQ,CAAC,WAAW,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAC7F;YAAAI,QAAA,EACJ;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;YACHqB,EAAE,EAAC,QAAQ;YACXF,SAAS,EAAE,yCACTH,QAAQ,CAAC,QAAQ,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAC1F;YAAAI,QAAA,EACJ;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;YACHqB,EAAE,EAAC,UAAU;YACbF,SAAS,EAAE,yCACTH,QAAQ,CAAC,UAAU,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAC5F;YAAAI,QAAA,EACJ;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENxB,OAAA;UAAKgB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1CjB,OAAA;YAAKgB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCjB,OAAA;cACEyB,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,WAAW;cACvBV,SAAS,EAAC;YAA6J;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC,eACFxB,OAAA;cAAQgB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eACrEjB,OAAA;gBAAK2B,KAAK,EAAC,4BAA4B;gBAACX,SAAS,EAAC,uBAAuB;gBAACY,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAb,QAAA,eAC7HjB,OAAA;kBAAM+B,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA6C;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxB,OAAA;YACEmC,OAAO,EAAEA,CAAA,KAAMxB,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;YAC1DM,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAExEjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACX,SAAS,EAAC,SAAS;cAACY,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAb,QAAA,eAC/GjB,OAAA;gBAAM+B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAA6C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGTxB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,OAAO;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBACzCjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACX,SAAS,EAAC,kEAAkE;cAACY,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAb,QAAA,eACxKjB,OAAA;gBAAM+B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAsJ;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3N,CAAC,eACNxB,OAAA;cAAMgB,SAAS,EAAC,iKAAiK;cAAAC,QAAA,EAC9Kd;YAAS;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGN,CAACtB,IAAI,gBACJF,OAAA;YAAKgB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDjB,OAAA,CAACH,IAAI;cAACqB,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,8GAA8G;cAAAC,QAAA,EAAC;YAE3I;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;cAACqB,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAEnI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENxB,OAAA;YAAKgB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBjB,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAAC,CAACD,aAAa,CAAE;cAChDU,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBAElHjB,OAAA;gBAAK2B,KAAK,EAAC,4BAA4B;gBAACX,SAAS,EAAC,SAAS;gBAACY,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAb,QAAA,eAC/GjB,OAAA;kBAAM+B,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAqE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I,CAAC,eACNxB,OAAA;gBAAKgB,SAAS,EAAE,6CAA6CV,aAAa,GAAG,YAAY,GAAG,EAAE,EAAG;gBAACsB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,aAAa;gBAAAZ,QAAA,eACzIjB,OAAA;kBAAMkC,CAAC,EAAC;gBAAwS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAERlB,aAAa,iBACZN,OAAA;cAAKgB,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjFjB,OAAA;gBAAKgB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDjB,OAAA;kBAAGgB,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3ExB,OAAA;kBAAGgB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAEf,IAAI,CAACkC;gBAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACNxB,OAAA,CAACH,IAAI;gBAACqB,EAAE,EAAC,UAAU;gBAACF,SAAS,EAAC,6FAA6F;gBAAAC,QAAA,eACzHjB,OAAA;kBAAKgB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjB,OAAA;oBAAK2B,KAAK,EAAC,4BAA4B;oBAACX,SAAS,EAAC,4BAA4B;oBAACY,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAb,QAAA,eAClIjB,OAAA;sBAAM+B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAqE;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1I,CAAC,WAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPxB,OAAA,CAACH,IAAI;gBAACqB,EAAE,EAAC,SAAS;gBAACF,SAAS,EAAC,6FAA6F;gBAAAC,QAAA,eACxHjB,OAAA;kBAAKgB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjB,OAAA;oBAAK2B,KAAK,EAAC,4BAA4B;oBAACX,SAAS,EAAC,4BAA4B;oBAACY,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAb,QAAA,eAClIjB,OAAA;sBAAM+B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAA4C;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjH,CAAC,aAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPxB,OAAA;gBAAKgB,SAAS,EAAC;cAA+B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDxB,OAAA;gBACEmC,OAAO,EAAE/B,QAAS;gBAClBY,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAExHjB,OAAA;kBAAKgB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjB,OAAA;oBAAK2B,KAAK,EAAC,4BAA4B;oBAACX,SAAS,EAAC,4BAA4B;oBAACY,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAb,QAAA,eAClIjB,OAAA;sBAAM+B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAA2F;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK,CAAC,UAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDxB,OAAA;YACEmC,OAAO,EAAEA,CAAA,KAAM1B,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDQ,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eAEpGjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACX,SAAS,EAAC,SAAS;cAACY,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAb,QAAA,eAC/GjB,OAAA;gBAAM+B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAyB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhB,gBAAgB,iBACfR,OAAA;MAAKgB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9DjB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjB,OAAA;UAAKgB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDjB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAE,wFAAwFH,QAAQ,CAAC,GAAG,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAAG;YAAAI,QAAA,gBACvMjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACU,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACT,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACG,WAAW,EAAC,GAAG;cAACF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAChB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC3NjB,OAAA;gBAAMkC,CAAC,EAAC;cAA4C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDxB,OAAA;gBAAMkC,CAAC,EAAC;cAA+G;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,eACNxB,OAAA;cAAAiB,QAAA,EAAM;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACPxB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,WAAW;YAACF,SAAS,EAAE,wFAAwFH,QAAQ,CAAC,WAAW,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAAG;YAAAI,QAAA,gBACvNjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACU,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACT,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACG,WAAW,EAAC,GAAG;cAACF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAChB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC3NjB,OAAA;gBAAMkC,CAAC,EAAC;cAAkB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCxB,OAAA;gBAAMkC,CAAC,EAAC;cAAwH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxIxB,OAAA;gBAAMkC,CAAC,EAAC;cAAoB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCxB,OAAA;gBAAMkC,CAAC,EAAC;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNxB,OAAA;cAAAiB,QAAA,EAAM;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACPxB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAE,wFAAwFH,QAAQ,CAAC,QAAQ,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAAG;YAAAI,QAAA,gBACjNjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACU,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACT,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACG,WAAW,EAAC,GAAG;cAACF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAChB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC3NjB,OAAA;gBAAQuC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACxCxB,OAAA;gBAAMkC,CAAC,EAAC;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BxB,OAAA;gBAAMkC,CAAC,EAAC;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNxB,OAAA;cAAAiB,QAAA,EAAM;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACPxB,OAAA,CAACH,IAAI;YAACqB,EAAE,EAAC,UAAU;YAACF,SAAS,EAAE,wFAAwFH,QAAQ,CAAC,UAAU,CAAC,GAAG,iCAAiC,GAAG,kCAAkC,EAAG;YAAAI,QAAA,gBACrNjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACU,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACT,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACG,WAAW,EAAC,GAAG;cAACF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAChB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,eAC3NjB,OAAA;gBAAMkC,CAAC,EAAC;cAA+R;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5S,CAAC,eACNxB,OAAA;cAAAiB,QAAA,EAAM;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACN,CAACtB,IAAI,iBACJF,OAAA;YAAKgB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCjB,OAAA,CAACH,IAAI;cAACqB,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,0HAA0H;cAAAC,QAAA,EAAC;YAEvJ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;cAACqB,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,+GAA+G;cAAAC,QAAA,EAAC;YAE/I;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAd,kBAAkB,iBACjBV,OAAA;MAAKgB,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDjB,OAAA;QAAKgB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCjB,OAAA;UAAKgB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBjB,OAAA;YACEyB,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,WAAW;YACvBV,SAAS,EAAC;UAA+J;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1K,CAAC,eACFxB,OAAA;YAAQgB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eACrEjB,OAAA;cAAK2B,KAAK,EAAC,4BAA4B;cAACX,SAAS,EAAC,uBAAuB;cAACY,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAb,QAAA,eAC7HjB,OAAA;gBAAM+B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAA6C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACnB,EAAA,CA5OIJ,MAAM;EAAA,QAIOH,WAAW;AAAA;AAAA4C,EAAA,GAJxBzC,MAAM;AA8OZ,eAAeA,MAAM;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}