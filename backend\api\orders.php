<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getOrder($_GET['id']);
        } else {
            getOrders();
        }
        break;
    case 'POST':
        createOrder();
        break;
    case 'PUT':
        if (isset($_GET['id'])) {
            updateOrder($_GET['id']);
        } else {
            sendResponse(['error' => 'Order ID required'], 400);
        }
        break;
    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteOrder($_GET['id']);
        } else {
            sendResponse(['error' => 'Order ID required'], 400);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getOrders() {
    $user = getCurrentUser();
    global $pdo;
    
    $whereClause = "";
    $params = [];
    
    // If not admin, only show user's orders
    if ($user['role'] !== 'admin') {
        $whereClause = "WHERE o.user_id = ?";
        $params[] = $user['id'];
    }
    
    $sql = "SELECT o.*, u.name as customer_name, u.email as customer_email,
                   COUNT(oi.id) as item_count
            FROM Orders o 
            LEFT JOIN Users u ON o.user_id = u.id 
            LEFT JOIN OrderItems oi ON o.id = oi.order_id 
            $whereClause 
            GROUP BY o.id 
            ORDER BY o.created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    foreach ($orders as &$order) {
        $order['total'] = (float)$order['total'];
    }
    
    sendResponse(['orders' => $orders]);
}

function getOrder($id) {
    $user = getCurrentUser();
    global $pdo;
    
    $whereClause = "WHERE o.id = ?";
    $params = [$id];
    
    // If not admin, only show user's orders
    if ($user['role'] !== 'admin') {
        $whereClause .= " AND o.user_id = ?";
        $params[] = $user['id'];
    }
    
    $stmt = $pdo->prepare("
        SELECT o.*, u.name as customer_name, u.email as customer_email
        FROM Orders o 
        LEFT JOIN Users u ON o.user_id = u.id 
        $whereClause
    ");
    $stmt->execute($params);
    $order = $stmt->fetch();
    
    if (!$order) {
        sendResponse(['error' => 'Order not found'], 404);
    }
    
    // Get order items
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, pi.image_path
        FROM OrderItems oi
        JOIN Products p ON oi.product_id = p.id
        LEFT JOIN ProductImages pi ON p.id = pi.product_id
        WHERE oi.order_id = ?
        GROUP BY oi.id
    ");
    $stmt->execute([$id]);
    $items = $stmt->fetchAll();
    
    $order['items'] = $items;
    $order['total'] = (float)$order['total'];
    
    sendResponse(['order' => $order]);
}

function createOrder() {
    $user = getCurrentUser();
    $data = json_decode(file_get_contents('php://input'), true);
    
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Get cart items
        $stmt = $pdo->prepare("SELECT id FROM Orders WHERE user_id = ? AND status = 'cart'");
        $stmt->execute([$user['id']]);
        $cartOrder = $stmt->fetch();
        
        if (!$cartOrder) {
            sendResponse(['error' => 'No items in cart'], 400);
        }
        
        // Calculate total
        $stmt = $pdo->prepare("
            SELECT SUM(oi.quantity * oi.price) as total
            FROM OrderItems oi
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$cartOrder['id']]);
        $total = $stmt->fetch()['total'];
        
        // Update order with shipping info and change status
        $stmt = $pdo->prepare("
            UPDATE Orders 
            SET status = 'pending', 
                total = ?,
                shipping_address = ?,
                payment_method = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->execute([
            $total,
            json_encode($data['shipping_address'] ?? []),
            $data['payment_method'] ?? 'cash_on_delivery',
            $cartOrder['id']
        ]);
        
        $pdo->commit();
        
        sendResponse(['message' => 'Order placed successfully', 'order_id' => $cartOrder['id']], 201);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        sendResponse(['error' => 'Failed to create order'], 500);
    }
}

function updateOrder($id) {
    requireAdmin();
    
    $data = json_decode(file_get_contents('php://input'), true);
    
    global $pdo;
    
    $fields = [];
    $params = [];
    
    if (isset($data['status'])) {
        $fields[] = "status = ?";
        $params[] = $data['status'];
    }
    
    if (isset($data['notes'])) {
        $fields[] = "notes = ?";
        $params[] = $data['notes'];
    }
    
    if (!empty($fields)) {
        $fields[] = "updated_at = CURRENT_TIMESTAMP";
        $params[] = $id;
        
        $sql = "UPDATE Orders SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(['error' => 'Order not found'], 404);
        }
    }
    
    sendResponse(['message' => 'Order updated successfully']);
}

function deleteOrder($id) {
    requireAdmin();
    
    global $pdo;
    
    $stmt = $pdo->prepare("DELETE FROM Orders WHERE id = ?");
    $stmt->execute([$id]);
    
    if ($stmt->rowCount() === 0) {
        sendResponse(['error' => 'Order not found'], 404);
    }
    
    sendResponse(['message' => 'Order deleted successfully']);
}
?>
