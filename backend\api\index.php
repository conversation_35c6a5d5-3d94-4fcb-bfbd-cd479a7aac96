<?php
require_once '../config/database.php';

// Get the request URI and method
$request_uri = $_SERVER['REQUEST_URI'];
$method = $_SERVER['REQUEST_METHOD'];

// Remove query string and decode URL
$path = parse_url($request_uri, PHP_URL_PATH);
$path = urldecode($path);

// Remove base path (adjust this based on your setup)
$base_path = '/api';
if (strpos($path, $base_path) === 0) {
    $path = substr($path, strlen($base_path));
}

// Remove leading slash
$path = ltrim($path, '/');

// Split path into segments
$segments = explode('/', $path);
$endpoint = $segments[0] ?? '';

// Route to appropriate handler
switch ($endpoint) {
    case 'auth':
        include 'auth.php';
        break;

    case 'products':
        include 'products.php';
        break;

    case 'categories':
        include 'categories.php';
        break;

    case 'cart':
        include 'cart.php';
        break;

    case 'orders':
        include 'orders.php';
        break;

    case 'users':
        include 'users.php';
        break;

    case 'contact':
        include 'contact.php';
        break;

    case 'settings':
        include 'settings.php';
        break;

    default:
        sendResponse(['error' => 'API endpoint not found', 'path' => $path], 404);
        break;
}
?>
