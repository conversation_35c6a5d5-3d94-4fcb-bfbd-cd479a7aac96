{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\ProductDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetails = ({\n  user,\n  onCartUpdate\n}) => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const [addingToCart, setAddingToCart] = useState(false);\n  useEffect(() => {\n    fetchProduct();\n  }, [id]);\n  const fetchProduct = async () => {\n    try {\n      const response = await axios.get(`/products?id=${id}`);\n      setProduct(response.data.product);\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      // Fallback to sample data\n      setProduct({\n        id: id,\n        name: 'Premium Anime Figure',\n        description: 'High-quality anime figure with detailed craftsmanship and authentic design.',\n        price: 89.99,\n        category_name: 'Figures',\n        images: ['/images/website/Kyo2.jpg'],\n        stock: 10\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Product not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.images[0],\n          alt: product.name,\n          className: \"w-full rounded-lg shadow-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-red-600 uppercase font-semibold mb-2\",\n          children: product.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-red-600 mb-6\",\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-2\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Quantity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setQuantity(Math.max(1, quantity - 1)),\n              className: \"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\",\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-16 text-center font-semibold\",\n              children: quantity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setQuantity(quantity + 1),\n              className: \"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 transition-colors font-semibold mb-4\",\n          children: \"Add to Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full border border-red-600 text-red-600 py-3 px-6 rounded-lg hover:bg-red-50 transition-colors font-semibold\",\n          children: \"Add to Favorites\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetails, \"0Hr3mks5G6j+jKhifBSbKHEVdrQ=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "ProductDetails", "user", "onCartUpdate", "_s", "id", "navigate", "product", "setProduct", "loading", "setLoading", "quantity", "setQuantity", "addingToCart", "setAddingToCart", "fetchProduct", "response", "get", "data", "error", "console", "name", "description", "price", "category_name", "images", "stock", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onClick", "Math", "max", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/ProductDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nconst ProductDetails = ({ user, onCartUpdate }) => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  const [addingToCart, setAddingToCart] = useState(false);\n\n  useEffect(() => {\n    fetchProduct();\n  }, [id]);\n\n  const fetchProduct = async () => {\n    try {\n      const response = await axios.get(`/products?id=${id}`);\n      setProduct(response.data.product);\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      // Fallback to sample data\n      setProduct({\n        id: id,\n        name: 'Premium Anime Figure',\n        description: 'High-quality anime figure with detailed craftsmanship and authentic design.',\n        price: 89.99,\n        category_name: 'Figures',\n        images: ['/images/website/Kyo2.jpg'],\n        stock: 10\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container py-8\">\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!product) {\n    return (\n      <div className=\"container py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Product not found</h1>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container py-8\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Product Images */}\n        <div>\n          <img\n            src={product.images[0]}\n            alt={product.name}\n            className=\"w-full rounded-lg shadow-lg\"\n          />\n        </div>\n\n        {/* Product Info */}\n        <div>\n          <p className=\"text-sm text-red-600 uppercase font-semibold mb-2\">\n            {product.category_name}\n          </p>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{product.name}</h1>\n          <p className=\"text-2xl font-bold text-red-600 mb-6\">${product.price}</p>\n          \n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Description</h3>\n            <p className=\"text-gray-600\">{product.description}</p>\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quantity\n            </label>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\"\n              >\n                -\n              </button>\n              <span className=\"w-16 text-center font-semibold\">{quantity}</span>\n              <button\n                onClick={() => setQuantity(quantity + 1)}\n                className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\"\n              >\n                +\n              </button>\n            </div>\n          </div>\n\n          <button className=\"w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 transition-colors font-semibold mb-4\">\n            Add to Cart\n          </button>\n\n          <button className=\"w-full border border-red-600 text-red-600 py-3 px-6 rounded-lg hover:bg-red-50 transition-colors font-semibold\">\n            Add to Favorites\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM;IAAEC;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdoB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;EAER,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,KAAK,CAACmB,GAAG,CAAC,gBAAgBZ,EAAE,EAAE,CAAC;MACtDG,UAAU,CAACQ,QAAQ,CAACE,IAAI,CAACX,OAAO,CAAC;IACnC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACAX,UAAU,CAAC;QACTH,EAAE,EAAEA,EAAE;QACNgB,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,6EAA6E;QAC1FC,KAAK,EAAE,KAAK;QACZC,aAAa,EAAE,SAAS;QACxBC,MAAM,EAAE,CAAC,0BAA0B,CAAC;QACpCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B5B,OAAA;QAAK2B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7D5B,OAAA;UAAK2B,SAAS,EAAC;QAA+D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACzB,OAAO,EAAE;IACZ,oBACEP,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B5B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B5B,OAAA;UAAI2B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAK2B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7B5B,OAAA;MAAK2B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD5B,OAAA;QAAA4B,QAAA,eACE5B,OAAA;UACEiC,GAAG,EAAE1B,OAAO,CAACkB,MAAM,CAAC,CAAC,CAAE;UACvBS,GAAG,EAAE3B,OAAO,CAACc,IAAK;UAClBM,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhC,OAAA;QAAA4B,QAAA,gBACE5B,OAAA;UAAG2B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC7DrB,OAAO,CAACiB;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACJhC,OAAA;UAAI2B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAErB,OAAO,CAACc;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzEhC,OAAA;UAAG2B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,GAAC,EAACrB,OAAO,CAACgB,KAAK;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAExEhC,OAAA;UAAK2B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5B,OAAA;YAAI2B,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEhC,OAAA;YAAG2B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAErB,OAAO,CAACe;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENhC,OAAA;UAAK2B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5B,OAAA;YAAO2B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRhC,OAAA;YAAK2B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5B,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE1B,QAAQ,GAAG,CAAC,CAAC,CAAE;cACtDgB,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA;cAAM2B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEjB;YAAQ;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClEhC,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;cACzCgB,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhC,OAAA;UAAQ2B,SAAS,EAAC,yGAAyG;UAAAC,QAAA,EAAC;QAE5H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThC,OAAA;UAAQ2B,SAAS,EAAC,gHAAgH;UAAAC,QAAA,EAAC;QAEnI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA9GIH,cAAc;EAAA,QACHL,SAAS,EACPC,WAAW;AAAA;AAAAyC,EAAA,GAFxBrC,cAAc;AAgHpB,eAAeA,cAAc;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}