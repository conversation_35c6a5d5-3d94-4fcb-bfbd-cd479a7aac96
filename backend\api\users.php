<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getUser($_GET['id']);
        } else {
            getUsers();
        }
        break;
    case 'PUT':
        if (isset($_GET['id'])) {
            updateUser($_GET['id']);
        } else {
            sendResponse(['error' => 'User ID required'], 400);
        }
        break;
    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteUser($_GET['id']);
        } else {
            sendResponse(['error' => 'User ID required'], 400);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getUsers() {
    requireAdmin();
    
    global $pdo;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $offset = ($page - 1) * $limit;
    $search = isset($_GET['search']) ? $_GET['search'] : null;
    $role = isset($_GET['role']) ? $_GET['role'] : null;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if ($search) {
        $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($role) {
        $whereClause .= " AND u.role = ?";
        $params[] = $role;
    }
    
    $sql = "SELECT u.*, COUNT(o.id) as order_count
            FROM Users u 
            LEFT JOIN Orders o ON u.id = o.user_id AND o.status != 'cart'
            $whereClause 
            GROUP BY u.id 
            ORDER BY u.created_at DESC 
            LIMIT $limit OFFSET $offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
    // Get total count
    $countSql = "SELECT COUNT(DISTINCT u.id) as total 
                 FROM Users u 
                 $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Remove passwords from response
    foreach ($users as &$user) {
        unset($user['password']);
        $user['order_count'] = (int)$user['order_count'];
    }
    
    sendResponse([
        'users' => $users,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => (int)$total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function getUser($id) {
    $currentUser = getCurrentUser();
    
    // Users can only view their own profile, admins can view any
    if ($currentUser['role'] !== 'admin' && $currentUser['id'] != $id) {
        sendResponse(['error' => 'Access denied'], 403);
    }
    
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT u.*, COUNT(o.id) as order_count
        FROM Users u 
        LEFT JOIN Orders o ON u.id = o.user_id AND o.status != 'cart'
        WHERE u.id = ? 
        GROUP BY u.id
    ");
    $stmt->execute([$id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendResponse(['error' => 'User not found'], 404);
    }
    
    unset($user['password']);
    $user['order_count'] = (int)$user['order_count'];
    
    sendResponse(['user' => $user]);
}

function updateUser($id) {
    $currentUser = getCurrentUser();
    
    // Users can only update their own profile, admins can update any
    if ($currentUser['role'] !== 'admin' && $currentUser['id'] != $id) {
        sendResponse(['error' => 'Access denied'], 403);
    }
    
    $data = json_decode(file_get_contents('php://input'), true);
    
    global $pdo;
    
    // Check if user exists
    $stmt = $pdo->prepare("SELECT id, role FROM Users WHERE id = ?");
    $stmt->execute([$id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendResponse(['error' => 'User not found'], 404);
    }
    
    $fields = [];
    $params = [];
    
    if (isset($data['name'])) {
        $fields[] = "name = ?";
        $params[] = $data['name'];
    }
    
    if (isset($data['email'])) {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM Users WHERE email = ? AND id != ?");
        $stmt->execute([$data['email'], $id]);
        if ($stmt->fetch()) {
            sendResponse(['error' => 'Email already exists'], 400);
        }
        
        $fields[] = "email = ?";
        $params[] = $data['email'];
    }
    
    if (isset($data['password']) && !empty($data['password'])) {
        $fields[] = "password = ?";
        $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
    }
    
    // Only admins can change roles
    if (isset($data['role']) && $currentUser['role'] === 'admin') {
        $fields[] = "role = ?";
        $params[] = $data['role'];
    }
    
    if (!empty($fields)) {
        $fields[] = "updated_at = CURRENT_TIMESTAMP";
        $params[] = $id;
        
        $sql = "UPDATE Users SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    }
    
    sendResponse(['message' => 'User updated successfully']);
}

function deleteUser($id) {
    requireAdmin();
    
    global $pdo;
    
    // Don't allow deleting the current admin
    $currentUser = getCurrentUser();
    if ($currentUser['id'] == $id) {
        sendResponse(['error' => 'Cannot delete your own account'], 400);
    }
    
    $stmt = $pdo->prepare("DELETE FROM Users WHERE id = ?");
    $stmt->execute([$id]);
    
    if ($stmt->rowCount() === 0) {
        sendResponse(['error' => 'User not found'], 404);
    }
    
    sendResponse(['message' => 'User deleted successfully']);
}
?>
