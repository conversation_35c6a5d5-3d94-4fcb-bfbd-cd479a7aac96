<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$host = 'localhost';
$dbname = 'kyopal';
$user = 'root';
$password = '';

// Create connection using mysqli
$mysqli = mysqli_connect($host, $user, $password, $dbname);

// Check connection
if (!$mysqli) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . mysqli_connect_error()]);
    exit();
}

// Set charset
mysqli_set_charset($mysqli, 'utf8');

// Helper function to send JSON response
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}

// Helper function to validate required fields
function validateRequired($data, $required) {
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(['error' => "Field '$field' is required"], 400);
        }
    }
}

// Helper function to get user from session/token
function getCurrentUser() {
    session_start();
    if (!isset($_SESSION['user'])) {
        sendResponse(['error' => 'Unauthorized'], 401);
    }
    return $_SESSION['user'];
}

// Helper function to check if user is admin
function requireAdmin() {
    $user = getCurrentUser();
    if ($user['role'] !== 'admin') {
        sendResponse(['error' => 'Admin access required'], 403);
    }
    return $user;
}

// Helper function to escape string
function escapeString($string) {
    global $mysqli;
    return mysqli_real_escape_string($mysqli, $string);
}

// Helper function to execute query
function executeQuery($query) {
    global $mysqli;
    $result = mysqli_query($mysqli, $query);
    if (!$result) {
        sendResponse(['error' => 'Database query failed: ' . mysqli_error($mysqli)], 500);
    }
    return $result;
}

// Helper function to get single row
function getSingleRow($query) {
    $result = executeQuery($query);
    return mysqli_fetch_assoc($result);
}

// Helper function to get all rows
function getAllRows($query) {
    $result = executeQuery($query);
    $rows = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $rows[] = $row;
    }
    return $rows;
}
