<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><?= $title ?? 'Dashboard' ?></title>
  <link rel="website icon" href="<?= "images/" ?>website/logo.png">
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body class="bg-gray-100">
  <div class="flex min-h-screen">
    <div id="sidebar" class="bg-gray-800 text-white w-64 min-h-screen flex flex-col fixed z-40 left-0 top-0 transform -translate-x-full lg:translate-x-0 transition-transform duration-300">
      <div class="p-4 border-b border-gray-700">
        <a href="/" class="flex items-center">
          <img src="<?= "images/" ?>website/logo.png" alt="KyoPal Logo" class="h-8" />
          <div class="ml-2">
            <div class="text-xl font-bold">KyoPal</div>
            <div class="text-xs text-gray-400">Admin Panel</div>
          </div>
        </a>
      </div>
      <nav class="flex-1 py-4">
        <ul class="gap-y-1">
          <li>
            <a href="/admin"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'dashboard' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Dashboard
            </a>
          </li>
          <li>
            <a href="/admin/products"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'products' || $requestedPage == 'add-product' || $requestedPage == 'edit-product' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
              </svg>
              Products
            </a>
          </li>
          <li>
            <a href="/admin/categories"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'categories' || $requestedPage == 'add-category' || $requestedPage == 'edit-category' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
              </svg>
              Categories
            </a>
          </li>
          <li>
            <a href="/admin/orders"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'orders' || $requestedPage == 'order-details' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              Orders
            </a>
          </li>
          <li>
            <a href="/admin/users"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'users' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              Users
            </a>
          </li>
          <li>
            <a href="/admin/messages"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'messages' || $requestedPage == 'view-message' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Messages
            </a>
          </li>
          <li>
            <a href="/admin/settings"
              class="flex items-center px-4 py-3 <?= $requestedPage == 'settings' ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700' ?>">
              <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              </svg>
              Settings
            </a>
          </li>
        </ul>
      </nav>
      <div class="p-4 border-t border-gray-700 text-sm text-gray-400">
        <a href="/" class="block hover:text-white mb-2 inline-flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Store
        </a>
        <div>KyoPal Admin v1.0</div>
      </div>
    </div>
    <div id="overlay" class="fixed inset-0 bg-black/30 z-30 hidden lg:hidden"></div>
    <div class="flex-1 transition-all duration-300 lg:ml-64">
      <header class="bg-white shadow-sm">
        <div class="flex justify-between items-center px-6 py-3">
          <button id="mobileMenuBtn" class="lg:hidden text-gray-600 hover:text-red-600 rounded-full cursor-pointer transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 class="text-2xl font-semibold text-gray-800">Admin Dashboard</h1>
        </div>
      </header>
<main class="p-6">