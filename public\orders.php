<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
if (!isset($_SESSION['user'])) {
    header('Location: /login.php');
    exit;
}
include_once(__DIR__ . '/../includes/store/header.php');
$title = "Orders";
$orders = [
    [
        'id' => 'ORD-2023-001',
        'date' => '2023-06-15',
        'status' => 'Delivered',
        'total' => 79.98,
        'items' => [
            ['id' => 1, 'name' => 'Anime Figure A', 'price' => 49.99, 'quantity' => 1, 'img' => 'assets/images/Kyo2.jpg'],
            ['id' => 2, 'name' => 'Manga Volume B', 'price' => 14.99, 'quantity' => 2, 'img' => 'assets/images/Kyo2.jpg']
        ],
        'shipping_address' => '123 Anime Street, Palestine',
        'payment_method' => 'Credit Card (ending in 4321)',
        'tracking_number' => 'TRK123456789'
    ],
    [
        'id' => 'ORD-2023-002',
        'date' => '2023-05-22',
        'status' => 'Processing',
        'total' => 49.99,
        'items' => [
            ['id' => 3, 'name' => 'Anime Poster C', 'price' => 24.99, 'quantity' => 1, 'img' => 'assets/images/Kyo2.jpg'],
            ['id' => 4, 'name' => 'Keychain D', 'price' => 12.50, 'quantity' => 2, 'img' => 'assets/images/Kyo2.jpg']
        ],
        'shipping_address' => '123 Anime Street, Palestine',
        'payment_method' => 'PayPal',
        'tracking_number' => null
    ],
    [
        'id' => 'ORD-2023-003',
        'date' => '2023-04-10',
        'status' => 'Delivered',
        'total' => 32.97,
        'items' => [
            ['id' => 5, 'name' => 'T-shirt E', 'price' => 32.97, 'quantity' => 1, 'img' => 'assets/images/Kyo2.jpg']
        ],
        'shipping_address' => '123 Anime Street, Palestine',
        'payment_method' => 'Credit Card (ending in 4321)',
        'tracking_number' => 'TRK987654321'
    ]
];

$selectedOrderId = isset($_GET['id']) ? $_GET['id'] : null;
$selectedOrder = null;

if ($selectedOrderId) {
    foreach ($orders as $order) {
        if ($order['id'] === $selectedOrderId) {
            $selectedOrder = $order;
            break;
        }
    }
}

// Set current page for sidebar highlighting
$requestedPage = "orders";

// Helper function to get status badge styling
function getStatusBadge($status)
{
    $badges = [
        'Delivered' => 'bg-green-100 text-green-800 border-green-200',
        'Processing' => 'bg-blue-100 text-blue-800 border-blue-200',
        'Shipped' => 'bg-purple-100 text-purple-800 border-purple-200',
        'Cancelled' => 'bg-red-100 text-red-800 border-red-200',
        'Pending' => 'bg-yellow-100 text-yellow-800 border-yellow-200'
    ];

    return $badges[$status] ?? 'bg-gray-100 text-gray-800 border-gray-200';
}
?>

<section class="container py-8 max-w-6xl mx-auto px-4">
    <div class="flex flex-col">
        <h1 class="text-3xl font-bold mb-6 text-gray-800">My Orders</h1>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <?php include_once(__DIR__ . '/../../includes/store/components/sidebar_profile.php'); ?>

            <div class="md:col-span-3">
                <?php if ($selectedOrder): /* SINGLE ORDER VIEW */ ?>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <!-- Order Header -->
                        <div class="bg-gradient-to-r from-red-500 to-red-600 p-5 text-white">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h2 class="text-xl font-bold">Order #<?= $selectedOrder['id'] ?></h2>
                                    <p class="opacity-90"><?= date('F j, Y', strtotime($selectedOrder['date'])) ?></p>
                                </div>
                                <span
                                    class="px-4 py-1.5 rounded-full bg-white <?= str_replace('border-', 'border-', getStatusBadge($selectedOrder['status'])) ?> font-medium text-sm">
                                    <?= $selectedOrder['status'] ?>
                                </span>
                            </div>
                        </div>

                        <!-- Order Info Cards -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 p-5 bg-gray-50">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <span class="text-xs uppercase text-gray-500 font-medium">Total Amount</span>
                                <p class="text-xl font-bold text-gray-800 mt-1">
                                    $<?= number_format($selectedOrder['total'], 2) ?></p>
                            </div>

                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <span class="text-xs uppercase text-gray-500 font-medium">Payment</span>
                                <p class="font-medium text-gray-800 mt-1"><?= $selectedOrder['payment_method'] ?></p>
                            </div>

                            <?php if ($selectedOrder['tracking_number']): ?>
                                <div class="bg-white p-4 rounded-lg shadow-sm">
                                    <span class="text-xs uppercase text-gray-500 font-medium">Tracking</span>
                                    <p class="font-medium text-gray-800 mt-1"><?= $selectedOrder['tracking_number'] ?></p>
                                </div>
                            <?php endif; ?>

                            <div class="bg-white p-4 rounded-lg shadow-sm col-span-full">
                                <span class="text-xs uppercase text-gray-500 font-medium">Shipping Address</span>
                                <p class="font-medium text-gray-800 mt-1">
                                    <?= nl2br(htmlspecialchars($selectedOrder['shipping_address'])) ?></p>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="p-5">
                            <h3 class="font-semibold text-gray-700 mb-4">Items in Your Order</h3>
                            <div class="space-y-3">
                                <?php foreach ($selectedOrder['items'] as $item): ?>
                                    <div
                                        class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100">
                                        <img src="<?= $item['img'] ?>" alt="<?= htmlspecialchars($item['name']) ?>"
                                            class="w-16 h-16 object-cover rounded-lg">
                                        <div class="ml-4 flex-1">
                                            <h4 class="font-medium text-gray-800"><?= htmlspecialchars($item['name']) ?></h4>
                                            <div class="flex justify-between mt-1">
                                                <span class="text-sm text-gray-500">Qty: <?= $item['quantity'] ?> ×
                                                    $<?= number_format($item['price'], 2) ?></span>
                                                <span
                                                    class="font-semibold text-gray-800">$<?= number_format($item['price'] * $item['quantity'], 2) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-100">
                                <a href="/orders.php" class="flex items-center text-gray-600 hover:text-red-500 transition">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Back to Orders
                                </a>

                                <?php if ($selectedOrder['status'] === 'Delivered'): ?>
                                    <button class="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition">
                                        Buy Again
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                <?php else: /* ORDERS LIST VIEW */ ?>
                    <?php if (empty($orders)): ?>
                        <div class="bg-white rounded-xl shadow-sm p-12 text-center">
                            <div
                                class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-50 text-red-500 mb-6">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-2">No Orders Yet</h3>
                            <p class="text-gray-500 mb-6">Looks like you haven't placed any orders yet.</p>
                            <a href="/products.php"
                                class="inline-block bg-red-500 text-white py-2.5 px-6 rounded-lg hover:bg-red-600 transition">
                                Start Shopping
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden divide-y divide-gray-100">
                            <?php foreach ($orders as $order): ?>
                                <div class="p-5 hover:bg-gray-50 transition-colors group">
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                        <div class="flex items-center">
                                            <div class="mr-4">
                                                <div
                                                    class="w-3 h-3 rounded-full <?= $order['status'] === 'Delivered' ? 'bg-green-500' : 'bg-blue-500' ?>">
                                                </div>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-800 group-hover:text-red-500 transition">
                                                    <a href="/orders.php?id=<?= $order['id'] ?>"><?= $order['id'] ?></a>
                                                </h3>
                                                <div class="flex items-center space-x-3 mt-1">
                                                    <span
                                                        class="text-sm text-gray-500"><?= date('M j, Y', strtotime($order['date'])) ?></span>
                                                    <span
                                                        class="inline-flex px-2.5 py-0.5 rounded-full text-xs border <?= getStatusBadge($order['status']) ?>">
                                                        <?= $order['status'] ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4 sm:mt-0 text-right">
                                            <span class="font-bold text-gray-800">$<?= number_format($order['total'], 2) ?></span>
                                            <p class="text-sm text-gray-500 mt-1"><?= count($order['items']) ?>
                                                <?= count($order['items']) > 1 ? 'items' : 'item' ?></p>
                                        </div>
                                    </div>

                                    <div class="mt-4 flex justify-between items-center">
                                        <div class="flex -space-x-2">
                                            <?php foreach (array_slice($order['items'], 0, 3) as $item): ?>
                                                <img src="<?= $item['img'] ?>" alt="<?= htmlspecialchars($item['name']) ?>"
                                                    class="w-10 h-10 object-cover rounded-full border-2 border-white shadow-sm">
                                            <?php endforeach; ?>

                                            <?php if (count($order['items']) > 3): ?>
                                                <div
                                                    class="w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-500">
                                                    +<?= count($order['items']) - 3 ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <a href="/orders.php?id=<?= $order['id'] ?>"
                                            class="inline-flex items-center text-sm font-medium text-red-500 hover:text-red-600">
                                            View Details
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>