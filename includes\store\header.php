<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Website' ?></title>
    <link rel="website icon" href="<?= "images/" ?>website/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merienda:wght@300..900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Merienda", cursive;
            font-optical-sizing: auto;
            font-style: normal;
            background-color: white;
            color: #111827;
        }
        .footer-link {
            color: gray;
            transition: color 0.3s ease;
        }
        .footer-link:hover {
            color: #dc2626;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        @media (max-width: 640px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body class="font-merienda min-h-screen flex flex-col">
<?php
$cartCount = 0;
if (isset($_SESSION['user'])) {
    $userId = $_SESSION['user']['id'];
    $cartResult = mysqli_query($mysqli, "SELECT SUM(quantity) as count FROM OrderItems oi JOIN Orders o ON oi.order_id = o.id WHERE o.user_id = $userId AND o.status = 'cart'");
    $cartCount = (int)mysqli_fetch_assoc($cartResult)['count'];
}
?>
<header class="sticky top-0 z-50 w-full shadow-md bg-white backdrop-blur supports-[backdrop-filter]:bg-white/80">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
            <div class="flex items-center">
                <a href="/" class="flex items-center space-x-2">
                    <img src="../assets/images/logo.png" alt="KYOPAL" class="w-10 h-10 rounded-full" />
                    <span class="inline-block text-xl font-bold text-black">KYOPAL</span>
                </a>
            </div>
            <nav class="hidden lg:flex space-x-6">
                <a href="/" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($requestedPage == 'home') echo 'text-red-600 hover:text-red-800'; ?>">Home</a>
                <a href="/products.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($requestedPage == 'products') echo 'text-red-600 hover:text-red-800'; ?>">Products</a>
                <a href="/about.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($requestedPage == 'about') echo 'text-red-600 hover:text-red-800'; ?>">About</a>
                <a href="/contact.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($requestedPage == 'contact') echo 'text-red-600 hover:text-red-800'; ?>">Contact</a>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="relative hidden sm:block">
                    <input type="text" placeholder="Search..."
                        class="w-64 px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300" />
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </div>
                <button id="mobileSearchBtn" class="sm:hidden text-gray-600 hover:text-red-600 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
                <a href="/cart.php" class="relative group">
                    <svg xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6 text-gray-600 group-hover:text-red-600 transition-colors" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span id="cart-count"
                        class="absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center group-hover:bg-red-700 transition-colors"><?php echo $cartCount; ?></span>
                </a>
                <div class="hidden sm:flex items-center space-x-2" <?php if (isset($_SESSION['user'])) echo 'style="display:none"'; ?>>
                    <a href="/login.php"
                        class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50">
                        Login
                    </a>
                    <a href="/register.php"
                        class="text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md">
                        Sign Up
                    </a>
                </div>
                <div class="relative" <?php if (!isset($_SESSION['user'])) echo 'style="display:none"'; ?>>
                    <button id="profileDropdownBtn"
                        class="text-gray-600 flex items-center hover:text-red-600 transition-colors focus:outline-none cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <svg id="arrowIcon" xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            class="h-3 w-3 transition-transform duration-300"
                            viewBox="0 0 330 330">
                            <path id="XMLID_225_"
                                d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393  c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393  s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z" />
                        </svg>
                    </button>
                    <div id="profileDropdown"
                        class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg py-2 z-50 hidden transform transition-transform duration-200 origin-top-right">
                        <div class="px-4 py-3 border-b border-gray-100">
                            <p class="text-sm leading-5 font-medium text-gray-900">User Account</p>
                            <p class="text-xs leading-4 font-normal text-gray-500 mt-1"><?php echo isset($_SESSION['user']) ? htmlspecialchars($_SESSION['user']['email']) : ''; ?></p>
                        </div>
                        <a href="/account.php" id="profile"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile
                            </div>
                        </a>
                        <a href="/orders.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                                My Orders
                            </div>
                        </a>
                        <div class="border-t border-gray-100 my-1"></div>
                        <a href="/logout.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </div>
                        </a>
                    </div>
                </div>
                <button id="mobileMenuBtn"
                    class="lg:hidden text-gray-600 hover:text-red-600 rounded-full cursor-pointer transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
    <div id="mobileMenu"
        class="max-h-0 opacity-0 lg:hidden border-t border-gray-200 rounded-b-xl overflow-hidden transition-all duration-300 ease-in-out">
        <div class="px-6">
            <nav class="flex flex-col divide-y divide-gray-200">
                <a href="/" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($requestedPage == 'home') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                        <path
                            d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                    </svg>
                    <span>Home</span>
                </a>
                <a href="/products.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($requestedPage == 'products') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path d="m7.5 4.27 9 5.15"></path>
                        <path
                            d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z">
                        </path>
                        <path d="m3.3 7 8.7 5 8.7-5"></path>
                        <path d="M12 22V12"></path>
                    </svg>
                    <span>Products</span>
                </a>
                <a href="/about.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($requestedPage == 'about') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 16v-4"></path>
                        <path d="M12 8h.01"></path>
                    </svg>
                    <span>About</span>
                </a>
                <a href="/contact.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($requestedPage == 'contact') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path
                            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                        </path>
                    </svg>
                    <span>Contact</span>
                </a>
                <div class="flex flex-col gap-2 py-4">
                    <a href="/login.php"
                        class="text-center text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50">
                        Login
                    </a>
                    <a href="/register.php"
                        class="text-center text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md">
                        Sign Up
                    </a>
                </div>
            </nav>
        </div>
    </div>
    <div id="mobileSearch"
        class="max-h-0 opacity-0 sm:hidden border-t border-gray-200 transition-all duration-300 ease-in-out overflow-hidden">
        <div class="mx-auto px-4 py-3">
            <div class="relative">
                <input type="text" placeholder="Search..."
                    class="w-full px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300" />
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</header>
<script>
    const profileBtn = document.getElementById('profileDropdownBtn');
    const profileDropdown = document.getElementById('profileDropdown');
    profileBtn.addEventListener('click', function () {
        profileDropdown.classList.toggle('hidden');
    });
    const btn = document.getElementById('profileDropdownBtn');
    const arrow = document.getElementById('arrowIcon');
    let isOpen = false;
    btn.addEventListener('click', function () {
        isOpen = !isOpen;
        arrow.classList.toggle('rotate-180', isOpen);
    });
</script>
<script>
if (!<?php echo isset($_SESSION['user']) ? 'true' : 'false'; ?>) {
    // If not logged in, get cart count from localStorage
    document.addEventListener('DOMContentLoaded', function() {
        var cart = JSON.parse(localStorage.getItem('cart') || '[]');
        document.getElementById('cart-count').textContent = cart.reduce((sum, item) => sum + (item.quantity || 1), 0);
    });
}
</script>
<main class="flex-1">