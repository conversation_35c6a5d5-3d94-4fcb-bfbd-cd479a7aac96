<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
if (!isset($_SESSION['user'])) {
    header('Location: /login.php');
    exit;
}
include_once(__DIR__ . '/../includes/store/header.php');
$title = "My Account";
$user = [
    'id' => 1,
    'username' => 'anime_fan',
    'first_name' => '<PERSON>',
    'last_name' => 'Doe',
    'email' => '<EMAIL>',
    'phone' => '+970 59 123 4567',
    'address' => '123 Anime Street, Palestine',
    'created_at' => '2023-01-15',
];
?>

<section class="container py-10">
    <div class="flex flex-col space-y-6">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold">My Account</h1>
            <p class="text-gray-600 mt-2">Manage your account settings and view orders</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <?php include_once(__DIR__ . '/../../includes/store/components/sidebar_profile.php');?>
            <div class="md:col-span-3">
                <div class="bg-white rounded-lg shadow-md p-6 border border-red-50">
                    <h2 class="text-xl font-bold mb-6">Profile Information</h2>

                    <form id="profile-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700">First
                                    Name</label>
                                <input type="text" id="first_name" name="first_name"
                                    value="<?= htmlspecialchars($user['first_name']) ?>"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                            </div>
                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                                <input type="text" id="last_name" name="last_name"
                                    value="<?= htmlspecialchars($user['last_name']) ?>"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                                <input type="text" id="username" name="username"
                                    value="<?= htmlspecialchars($user['username']) ?>"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" id="email" name="email"
                                    value="<?= htmlspecialchars($user['email']) ?>"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                            </div>
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                            <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($user['phone']) ?>"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                        </div>

                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                            <textarea id="address" name="address" rows="3"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"><?= htmlspecialchars($user['address']) ?></textarea>
                        </div>

                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700">Current
                                Password (required to save changes)</label>
                            <input type="password" id="current_password" name="current_password"
                                placeholder="Enter your current password"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                class="bg-red-600 text-white py-2 px-6 rounded-md hover:bg-red-700 transition">
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 mt-6 border border-red-50">
                    <h2 class="text-xl font-bold mb-6">Change Password</h2>

                    <form id="password-form" class="space-y-6">
                        <div>
                            <label for="old_password" class="block text-sm font-medium text-gray-700">Current
                                Password</label>
                            <input type="password" id="old_password" name="old_password"
                                placeholder="Enter your current password"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                        </div>

                        <div>
                            <label for="new_password" class="block text-sm font-medium text-gray-700">New
                                Password</label>
                            <input type="password" id="new_password" name="new_password"
                                placeholder="Enter your new password"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                        </div>

                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm New
                                Password</label>
                            <input type="password" id="confirm_password" name="confirm_password"
                                placeholder="Confirm your new password"
                                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                class="bg-red-600 text-white py-2 px-6 rounded-md hover:bg-red-700 transition">
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>