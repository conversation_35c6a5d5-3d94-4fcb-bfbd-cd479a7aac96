<?php
/*
-- 🧍‍♂️ Users
CREATE TABLE Users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON>R(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🗂️ Categories
CREATE TABLE Categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    image_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📦 Products
CREATE TABLE Products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    category_id INT REFERENCES Categories(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🖼️ ProductImages
CREATE TABLE ProductImages (
    id SERIAL PRIMARY KEY,
    product_id INT REFERENCES Products(id) ON DELETE CASCADE,
    image_path VARCHAR(255) NOT NULL
);

-- ❤️ Favorites
CREATE TABLE Favorites (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES Users(id) ON DELETE CASCADE,
    product_id INT REFERENCES Products(id) ON DELETE CASCADE,
    UNIQUE(user_id, product_id)
);

-- 📬 ContactMessages
CREATE TABLE ContactMessages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(150),
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🧾 Orders
CREATE TABLE Orders (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES Users(id) ON DELETE RESTRICT,
    status VARCHAR(20) DEFAULT 'cart' CHECK (status IN ('cart', 'pending', 'shipped', 'delivered', 'cancelled')),
    total_price DECIMAL(10,2) CHECK (total_price >= 0),
    shipping_address TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🛒 OrderItems
CREATE TABLE OrderItems (
    id SERIAL PRIMARY KEY,
    order_id INT REFERENCES Orders(id) ON DELETE CASCADE,
    product_id INT REFERENCES Products(id),
    quantity INT DEFAULT 1 CHECK (quantity > 0),
    price DECIMAL(10,2) CHECK (price >= 0)
);

-- 🏫 StoreSettings
CREATE TABLE StoreSettings (
    id SERIAL PRIMARY KEY,
    store_name VARCHAR(100),
    logo_path VARCHAR(255),
    email VARCHAR(100),
    phone_number VARCHAR(20),
    location TEXT,
    facebook_url VARCHAR(255),
    instagram_url VARCHAR(255),
    tiktok_url VARCHAR(255),
    whatsapp_url VARCHAR(255),
    telegram_url VARCHAR(255),
    x_url VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
*/

$host = 'localhost';
$dbname = 'kyopal';
$user = 'root';
$password = '';

$mysqli = mysqli_connect($host, $user, $password, $dbname);
?>