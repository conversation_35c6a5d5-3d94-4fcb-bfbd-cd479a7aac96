{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\ProductDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetails = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setProduct({\n        id: id,\n        name: 'Premium Anime Figure',\n        description: 'High-quality anime figure with detailed craftsmanship and authentic design.',\n        price: 89.99,\n        category_name: 'Figures',\n        images: ['/images/website/Kyo2.jpg', '/images/website/Kyo2.jpg']\n      });\n      setLoading(false);\n    }, 1000);\n  }, [id]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Product not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.images[0],\n          alt: product.name,\n          className: \"w-full rounded-lg shadow-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-red-600 uppercase font-semibold mb-2\",\n          children: product.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-red-600 mb-6\",\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-2\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Quantity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setQuantity(Math.max(1, quantity - 1)),\n              className: \"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\",\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-16 text-center font-semibold\",\n              children: quantity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setQuantity(quantity + 1),\n              className: \"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 transition-colors font-semibold mb-4\",\n          children: \"Add to Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full border border-red-600 text-red-600 py-3 px-6 rounded-lg hover:bg-red-50 transition-colors font-semibold\",\n          children: \"Add to Favorites\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetails, \"4cSOcRkw7ULFl94PIMYi1LDSkLk=\", false, function () {\n  return [useParams];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "jsxDEV", "_jsxDEV", "ProductDetails", "_s", "id", "product", "setProduct", "loading", "setLoading", "quantity", "setQuantity", "setTimeout", "name", "description", "price", "category_name", "images", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onClick", "Math", "max", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/ProductDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\n\nconst ProductDetails = () => {\n  const { id } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [quantity, setQuantity] = useState(1);\n\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setProduct({\n        id: id,\n        name: 'Premium Anime Figure',\n        description: 'High-quality anime figure with detailed craftsmanship and authentic design.',\n        price: 89.99,\n        category_name: 'Figures',\n        images: ['/images/website/Kyo2.jpg', '/images/website/Kyo2.jpg']\n      });\n      setLoading(false);\n    }, 1000);\n  }, [id]);\n\n  if (loading) {\n    return (\n      <div className=\"container py-8\">\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!product) {\n    return (\n      <div className=\"container py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Product not found</h1>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container py-8\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Product Images */}\n        <div>\n          <img\n            src={product.images[0]}\n            alt={product.name}\n            className=\"w-full rounded-lg shadow-lg\"\n          />\n        </div>\n\n        {/* Product Info */}\n        <div>\n          <p className=\"text-sm text-red-600 uppercase font-semibold mb-2\">\n            {product.category_name}\n          </p>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{product.name}</h1>\n          <p className=\"text-2xl font-bold text-red-600 mb-6\">${product.price}</p>\n          \n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Description</h3>\n            <p className=\"text-gray-600\">{product.description}</p>\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quantity\n            </label>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\"\n              >\n                -\n              </button>\n              <span className=\"w-16 text-center font-semibold\">{quantity}</span>\n              <button\n                onClick={() => setQuantity(quantity + 1)}\n                className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300\"\n              >\n                +\n              </button>\n            </div>\n          </div>\n\n          <button className=\"w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 transition-colors font-semibold mb-4\">\n            Add to Cart\n          </button>\n\n          <button className=\"w-full border border-red-600 text-red-600 py-3 px-6 rounded-lg hover:bg-red-50 transition-colors font-semibold\">\n            Add to Favorites\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAG,CAAC,GAAGL,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd;IACAa,UAAU,CAAC,MAAM;MACfL,UAAU,CAAC;QACTF,EAAE,EAAEA,EAAE;QACNQ,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,6EAA6E;QAC1FC,KAAK,EAAE,KAAK;QACZC,aAAa,EAAE,SAAS;QACxBC,MAAM,EAAE,CAAC,0BAA0B,EAAE,0BAA0B;MACjE,CAAC,CAAC;MACFR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACJ,EAAE,CAAC,CAAC;EAER,IAAIG,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKgB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BjB,OAAA;QAAKgB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DjB,OAAA;UAAKgB,SAAS,EAAC;QAA+D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACjB,OAAO,EAAE;IACZ,oBACEJ,OAAA;MAAKgB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BjB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BjB,OAAA;UAAIgB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErB,OAAA;IAAKgB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BjB,OAAA;MAAKgB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDjB,OAAA;QAAAiB,QAAA,eACEjB,OAAA;UACEsB,GAAG,EAAElB,OAAO,CAACW,MAAM,CAAC,CAAC,CAAE;UACvBQ,GAAG,EAAEnB,OAAO,CAACO,IAAK;UAClBK,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrB,OAAA;QAAAiB,QAAA,gBACEjB,OAAA;UAAGgB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC7Db,OAAO,CAACU;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACJrB,OAAA;UAAIgB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAEb,OAAO,CAACO;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzErB,OAAA;UAAGgB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,GAAC,EAACb,OAAO,CAACS,KAAK;QAAA;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAExErB,OAAA;UAAKgB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjB,OAAA;YAAIgB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzErB,OAAA;YAAGgB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEb,OAAO,CAACQ;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENrB,OAAA;UAAKgB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjB,OAAA;YAAOgB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrB,OAAA;YAAKgB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CjB,OAAA;cACEwB,OAAO,EAAEA,CAAA,KAAMf,WAAW,CAACgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElB,QAAQ,GAAG,CAAC,CAAC,CAAE;cACtDQ,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrB,OAAA;cAAMgB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAET;YAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClErB,OAAA;cACEwB,OAAO,EAAEA,CAAA,KAAMf,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;cACzCQ,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAClG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrB,OAAA;UAAQgB,SAAS,EAAC,yGAAyG;UAAAC,QAAA,EAAC;QAE5H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrB,OAAA;UAAQgB,SAAS,EAAC,gHAAgH;UAAAC,QAAA,EAAC;QAEnI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAlGID,cAAc;EAAA,QACHH,SAAS;AAAA;AAAA6B,EAAA,GADpB1B,cAAc;AAoGpB,eAAeA,cAAc;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}