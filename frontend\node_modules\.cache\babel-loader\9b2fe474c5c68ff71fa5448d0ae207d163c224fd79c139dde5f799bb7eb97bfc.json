{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\FAQ.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FAQ = () => {\n  _s();\n  const [openIndex, setOpenIndex] = useState(null);\n  const faqs = [{\n    question: \"What types of anime merchandise do you sell?\",\n    answer: \"We offer a wide variety of anime merchandise including figures, clothing, accessories, manga, posters, keychains, plushies, and limited edition collectibles from popular anime series.\"\n  }, {\n    question: \"Are your products authentic?\",\n    answer: \"Yes, all our products are 100% authentic. We source directly from official manufacturers and authorized distributors to ensure quality and authenticity.\"\n  }, {\n    question: \"How long does shipping take?\",\n    answer: \"Shipping typically takes 3-7 business days within Palestine. We offer free shipping on orders over $50.\"\n  }, {\n    question: \"What payment methods do you accept?\",\n    answer: \"We accept major credit cards, PayPal, and cash on delivery for local orders.\"\n  }, {\n    question: \"Can I return or exchange items?\",\n    answer: \"Yes, we offer a 30-day return policy for unused items in original packaging. Please contact our customer service for return instructions.\"\n  }, {\n    question: \"Do you offer international shipping?\",\n    answer: \"Currently, we primarily serve Palestine, but we're working on expanding our shipping options to neighboring countries.\"\n  }];\n  const toggleFAQ = index => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-3xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-gray-900 mb-8 text-center\",\n        children: \"Frequently Asked Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => toggleFAQ(index),\n            className: \"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-900\",\n              children: faq.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: `w-5 h-5 transform transition-transform ${openIndex === index ? 'rotate-180' : ''}`,\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M19 9l-7 7-7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this), openIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 pb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: faq.answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-12 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Still have questions?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Can't find the answer you're looking for? Please contact our customer support team.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/contact\",\n          className: \"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors\",\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(FAQ, \"7z1SfW1ag/kVV/D8SOtFgmPOJ8o=\");\n_c = FAQ;\nexport default FAQ;\nvar _c;\n$RefreshReg$(_c, \"FAQ\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "FAQ", "_s", "openIndex", "setOpenIndex", "faqs", "question", "answer", "toggleFAQ", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "faq", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/FAQ.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst FAQ = () => {\n  const [openIndex, setOpenIndex] = useState(null);\n\n  const faqs = [\n    {\n      question: \"What types of anime merchandise do you sell?\",\n      answer: \"We offer a wide variety of anime merchandise including figures, clothing, accessories, manga, posters, keychains, plushies, and limited edition collectibles from popular anime series.\"\n    },\n    {\n      question: \"Are your products authentic?\",\n      answer: \"Yes, all our products are 100% authentic. We source directly from official manufacturers and authorized distributors to ensure quality and authenticity.\"\n    },\n    {\n      question: \"How long does shipping take?\",\n      answer: \"Shipping typically takes 3-7 business days within Palestine. We offer free shipping on orders over $50.\"\n    },\n    {\n      question: \"What payment methods do you accept?\",\n      answer: \"We accept major credit cards, PayPal, and cash on delivery for local orders.\"\n    },\n    {\n      question: \"Can I return or exchange items?\",\n      answer: \"Yes, we offer a 30-day return policy for unused items in original packaging. Please contact our customer service for return instructions.\"\n    },\n    {\n      question: \"Do you offer international shipping?\",\n      answer: \"Currently, we primarily serve Palestine, but we're working on expanding our shipping options to neighboring countries.\"\n    }\n  ];\n\n  const toggleFAQ = (index) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  return (\n    <div className=\"container py-8\">\n      <div className=\"max-w-3xl mx-auto\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-8 text-center\">\n          Frequently Asked Questions\n        </h1>\n        \n        <div className=\"space-y-4\">\n          {faqs.map((faq, index) => (\n            <div key={index} className=\"bg-white rounded-lg shadow-md\">\n              <button\n                onClick={() => toggleFAQ(index)}\n                className=\"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none\"\n              >\n                <span className=\"font-semibold text-gray-900\">{faq.question}</span>\n                <svg\n                  className={`w-5 h-5 transform transition-transform ${\n                    openIndex === index ? 'rotate-180' : ''\n                  }`}\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              {openIndex === index && (\n                <div className=\"px-6 pb-4\">\n                  <p className=\"text-gray-600\">{faq.answer}</p>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n        \n        <div className=\"mt-12 text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Still have questions?</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Can't find the answer you're looking for? Please contact our customer support team.\n          </p>\n          <a\n            href=\"/contact\"\n            className=\"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors\"\n          >\n            Contact Us\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FAQ;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMO,IAAI,GAAG,CACX;IACEC,QAAQ,EAAE,8CAA8C;IACxDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,8BAA8B;IACxCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,8BAA8B;IACxCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,qCAAqC;IAC/CC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,iCAAiC;IAC3CC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,SAAS,GAAIC,KAAK,IAAK;IAC3BL,YAAY,CAACD,SAAS,KAAKM,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAClD,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BX,OAAA;MAAKU,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCX,OAAA;QAAIU,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELf,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBN,IAAI,CAACW,GAAG,CAAC,CAACC,GAAG,EAAER,KAAK,kBACnBT,OAAA;UAAiBU,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBACxDX,OAAA;YACEkB,OAAO,EAAEA,CAAA,KAAMV,SAAS,CAACC,KAAK,CAAE;YAChCC,SAAS,EAAC,kGAAkG;YAAAC,QAAA,gBAE5GX,OAAA;cAAMU,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAEM,GAAG,CAACX;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnEf,OAAA;cACEU,SAAS,EAAE,0CACTP,SAAS,KAAKM,KAAK,GAAG,YAAY,GAAG,EAAE,EACtC;cACHU,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cAAAV,QAAA,eAEnBX,OAAA;gBAAMsB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAgB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EACRZ,SAAS,KAAKM,KAAK,iBAClBT,OAAA;YAAKU,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBX,OAAA;cAAGU,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEM,GAAG,CAACV;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACN;QAAA,GArBON,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCX,OAAA;UAAIU,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFf,OAAA;UAAGU,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJf,OAAA;UACE0B,IAAI,EAAC,UAAU;UACfhB,SAAS,EAAC,+EAA+E;UAAAC,QAAA,EAC1F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CApFID,GAAG;AAAA0B,EAAA,GAAH1B,GAAG;AAsFT,eAAeA,GAAG;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}