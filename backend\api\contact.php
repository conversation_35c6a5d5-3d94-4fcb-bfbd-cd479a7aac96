<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getMessages();
        break;
    case 'POST':
        createMessage();
        break;
    case 'PUT':
        if (isset($_GET['id'])) {
            updateMessage($_GET['id']);
        } else {
            sendResponse(['error' => 'Message ID required'], 400);
        }
        break;
    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteMessage($_GET['id']);
        } else {
            sendResponse(['error' => 'Message ID required'], 400);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getMessages() {
    requireAdmin();
    
    global $pdo;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $offset = ($page - 1) * $limit;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if ($status === 'read') {
        $whereClause .= " AND read_status = 1";
    } elseif ($status === 'unread') {
        $whereClause .= " AND read_status = 0";
    }
    
    $sql = "SELECT * FROM ContactMessages 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $messages = $stmt->fetchAll();
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM ContactMessages $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    foreach ($messages as &$message) {
        $message['read_status'] = (bool)$message['read_status'];
    }
    
    sendResponse([
        'messages' => $messages,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => (int)$total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function createMessage() {
    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['name', 'email', 'subject', 'message']);
    
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO ContactMessages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        $data['name'],
        $data['email'],
        $data['subject'],
        $data['message']
    ]);
    
    $messageId = $pdo->lastInsertId();
    
    sendResponse(['message' => 'Message sent successfully', 'id' => $messageId], 201);
}

function updateMessage($id) {
    requireAdmin();
    
    $data = json_decode(file_get_contents('php://input'), true);
    
    global $pdo;
    
    $fields = [];
    $params = [];
    
    if (isset($data['read_status'])) {
        $fields[] = "read_status = ?";
        $params[] = $data['read_status'] ? 1 : 0;
    }
    
    if (!empty($fields)) {
        $params[] = $id;
        $sql = "UPDATE ContactMessages SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(['error' => 'Message not found'], 404);
        }
    }
    
    sendResponse(['message' => 'Message updated successfully']);
}

function deleteMessage($id) {
    requireAdmin();
    
    global $pdo;
    
    $stmt = $pdo->prepare("DELETE FROM ContactMessages WHERE id = ?");
    $stmt->execute([$id]);
    
    if ($stmt->rowCount() === 0) {
        sendResponse(['error' => 'Message not found'], 404);
    }
    
    sendResponse(['message' => 'Message deleted successfully']);
}
?>
