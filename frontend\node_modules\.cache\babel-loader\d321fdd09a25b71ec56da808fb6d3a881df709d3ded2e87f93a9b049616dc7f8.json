{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\Terms.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Terms = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-gray-900 mb-8 text-center\",\n        children: \"Terms and Conditions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prose max-w-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-8 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"1. Acceptance of Terms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"By accessing and using KYOPAL website, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"2. Products and Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"KYOPAL provides anime merchandise including figures, clothing, accessories, and collectibles. All product descriptions, images, and prices are subject to change without notice. We reserve the right to discontinue any product at any time.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"3. Ordering and Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"When you place an order, you agree to provide accurate and complete information. Payment must be made in full before shipping. We accept major credit cards, PayPal, and cash on delivery for local orders.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"4. Shipping and Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We aim to process and ship orders within 1-2 business days. Delivery times may vary based on location and shipping method selected. Risk of loss and title for items pass to you upon delivery to the carrier.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"5. Returns and Refunds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Items may be returned within 30 days of purchase in original condition and packaging. Refunds will be processed to the original payment method within 5-10 business days after we receive the returned item.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"6. Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the website, to understand our practices.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"7. Limitation of Liability\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"KYOPAL shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"8. Changes to Terms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting on the website. Your continued use of the website constitutes acceptance of the modified terms.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"9. Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"If you have any questions about these Terms and Conditions, please contact <NAME_EMAIL> or through our contact page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Last updated: January 2024\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Terms;\nexport default Terms;\nvar _c;\n$RefreshReg$(_c, \"Terms\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Terms", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/Terms.js"], "sourcesContent": ["import React from 'react';\n\nconst Terms = () => {\n  return (\n    <div className=\"container py-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-8 text-center\">\n          Terms and Conditions\n        </h1>\n        \n        <div className=\"prose max-w-none\">\n          <div className=\"bg-white rounded-lg shadow-md p-8 space-y-6\">\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">1. Acceptance of Terms</h2>\n              <p className=\"text-gray-600\">\n                By accessing and using KYOPAL website, you accept and agree to be bound by the terms \n                and provision of this agreement. If you do not agree to abide by the above, please \n                do not use this service.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">2. Products and Services</h2>\n              <p className=\"text-gray-600\">\n                KYOPAL provides anime merchandise including figures, clothing, accessories, and \n                collectibles. All product descriptions, images, and prices are subject to change \n                without notice. We reserve the right to discontinue any product at any time.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">3. Ordering and Payment</h2>\n              <p className=\"text-gray-600\">\n                When you place an order, you agree to provide accurate and complete information. \n                Payment must be made in full before shipping. We accept major credit cards, PayPal, \n                and cash on delivery for local orders.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">4. Shipping and Delivery</h2>\n              <p className=\"text-gray-600\">\n                We aim to process and ship orders within 1-2 business days. Delivery times may vary \n                based on location and shipping method selected. Risk of loss and title for items \n                pass to you upon delivery to the carrier.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">5. Returns and Refunds</h2>\n              <p className=\"text-gray-600\">\n                Items may be returned within 30 days of purchase in original condition and packaging. \n                Refunds will be processed to the original payment method within 5-10 business days \n                after we receive the returned item.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">6. Privacy Policy</h2>\n              <p className=\"text-gray-600\">\n                Your privacy is important to us. Please review our Privacy Policy, which also governs \n                your use of the website, to understand our practices.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">7. Limitation of Liability</h2>\n              <p className=\"text-gray-600\">\n                KYOPAL shall not be liable for any indirect, incidental, special, consequential, \n                or punitive damages, including without limitation, loss of profits, data, use, \n                goodwill, or other intangible losses.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">8. Changes to Terms</h2>\n              <p className=\"text-gray-600\">\n                We reserve the right to modify these terms at any time. Changes will be effective \n                immediately upon posting on the website. Your continued use of the website constitutes \n                acceptance of the modified terms.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">9. Contact Information</h2>\n              <p className=\"text-gray-600\">\n                If you have any questions about these Terms and Conditions, please contact us at \n                <EMAIL> or through our contact page.\n              </p>\n            </section>\n          </div>\n        </div>\n        \n        <div className=\"mt-8 text-center\">\n          <p className=\"text-gray-500\">\n            Last updated: January 2024\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Terms;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCH,OAAA;QAAIE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAKE,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DH,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAG7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAG7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAnGIP,KAAK;AAqGX,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}