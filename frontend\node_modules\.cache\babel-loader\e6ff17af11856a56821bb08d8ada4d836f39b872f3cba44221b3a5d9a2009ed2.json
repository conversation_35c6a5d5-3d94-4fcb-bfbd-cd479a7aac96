{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\Privacy.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Privacy = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-gray-900 mb-8 text-center\",\n        children: \"Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prose max-w-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-8 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"1. Information We Collect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-3\",\n              children: \"We collect information you provide directly to us, such as when you:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside text-gray-600 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Create an account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Make a purchase\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Contact us for support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Subscribe to our newsletter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"2. How We Use Your Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-3\",\n              children: \"We use the information we collect to:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside text-gray-600 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Process and fulfill your orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Communicate with you about your account or orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Provide customer support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Send you promotional emails (with your consent)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Improve our website and services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"3. Information Sharing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information with trusted service providers who assist us in operating our website and conducting our business.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"4. Data Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"5. Cookies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We use cookies to enhance your experience on our website. Cookies help us remember your preferences and understand how you use our site. You can choose to disable cookies in your browser settings.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"6. Your Rights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-3\",\n              children: \"You have the right to:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside text-gray-600 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Access your personal information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Correct inaccurate information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Delete your account and personal information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Opt out of marketing communications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"7. Children's Privacy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Our website is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"8. Changes to This Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the \\\"last updated\\\" date.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: \"9. Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"If you have any questions about this Privacy Policy, please contact <NAME_EMAIL> or through our contact page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Last updated: January 2024\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Privacy;\nexport default Privacy;\nvar _c;\n$RefreshReg$(_c, \"Privacy\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Privacy", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/Privacy.js"], "sourcesContent": ["import React from 'react';\n\nconst Privacy = () => {\n  return (\n    <div className=\"container py-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-8 text-center\">\n          Privacy Policy\n        </h1>\n        \n        <div className=\"prose max-w-none\">\n          <div className=\"bg-white rounded-lg shadow-md p-8 space-y-6\">\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">1. Information We Collect</h2>\n              <p className=\"text-gray-600 mb-3\">\n                We collect information you provide directly to us, such as when you:\n              </p>\n              <ul className=\"list-disc list-inside text-gray-600 space-y-1\">\n                <li>Create an account</li>\n                <li>Make a purchase</li>\n                <li>Contact us for support</li>\n                <li>Subscribe to our newsletter</li>\n              </ul>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">2. How We Use Your Information</h2>\n              <p className=\"text-gray-600 mb-3\">\n                We use the information we collect to:\n              </p>\n              <ul className=\"list-disc list-inside text-gray-600 space-y-1\">\n                <li>Process and fulfill your orders</li>\n                <li>Communicate with you about your account or orders</li>\n                <li>Provide customer support</li>\n                <li>Send you promotional emails (with your consent)</li>\n                <li>Improve our website and services</li>\n              </ul>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">3. Information Sharing</h2>\n              <p className=\"text-gray-600\">\n                We do not sell, trade, or otherwise transfer your personal information to third parties \n                without your consent, except as described in this policy. We may share your information \n                with trusted service providers who assist us in operating our website and conducting \n                our business.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">4. Data Security</h2>\n              <p className=\"text-gray-600\">\n                We implement appropriate security measures to protect your personal information against \n                unauthorized access, alteration, disclosure, or destruction. However, no method of \n                transmission over the internet is 100% secure.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">5. Cookies</h2>\n              <p className=\"text-gray-600\">\n                We use cookies to enhance your experience on our website. Cookies help us remember \n                your preferences and understand how you use our site. You can choose to disable \n                cookies in your browser settings.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">6. Your Rights</h2>\n              <p className=\"text-gray-600 mb-3\">\n                You have the right to:\n              </p>\n              <ul className=\"list-disc list-inside text-gray-600 space-y-1\">\n                <li>Access your personal information</li>\n                <li>Correct inaccurate information</li>\n                <li>Delete your account and personal information</li>\n                <li>Opt out of marketing communications</li>\n              </ul>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">7. Children's Privacy</h2>\n              <p className=\"text-gray-600\">\n                Our website is not intended for children under 13 years of age. We do not knowingly \n                collect personal information from children under 13. If you are a parent or guardian \n                and believe your child has provided us with personal information, please contact us.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">8. Changes to This Policy</h2>\n              <p className=\"text-gray-600\">\n                We may update this privacy policy from time to time. We will notify you of any \n                changes by posting the new policy on this page and updating the \"last updated\" date.\n              </p>\n            </section>\n\n            <section>\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">9. Contact Us</h2>\n              <p className=\"text-gray-600\">\n                If you have any questions about this Privacy Policy, please contact us at \n                <EMAIL> or through our contact page.\n              </p>\n            </section>\n          </div>\n        </div>\n        \n        <div className=\"mt-8 text-center\">\n          <p className=\"text-gray-500\">\n            Last updated: January 2024\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Privacy;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCH,OAAA;QAAIE,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAKE,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DH,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFP,OAAA;cAAGE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAIE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC3DH,OAAA;gBAAAG,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BP,OAAA;gBAAAG,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBP,OAAA;gBAAAG,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BP,OAAA;gBAAAG,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFP,OAAA;cAAGE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAIE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC3DH,OAAA;gBAAAG,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCP,OAAA;gBAAAG,QAAA,EAAI;cAAiD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DP,OAAA;gBAAAG,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCP,OAAA;gBAAAG,QAAA,EAAI;cAA+C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDP,OAAA;gBAAAG,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAK7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEP,OAAA;cAAGE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAIE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC3DH,OAAA;gBAAAG,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCP,OAAA;gBAAAG,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCP,OAAA;gBAAAG,QAAA,EAAI;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDP,OAAA;gBAAAG,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAG7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEVP,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAG7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAjHIP,OAAO;AAmHb,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}