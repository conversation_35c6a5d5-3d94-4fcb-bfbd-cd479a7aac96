{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\Checkout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Checkout = ({\n  user\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    phone: '',\n    address: '',\n    city: '',\n    postalCode: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n\n    // Simulate order processing\n    setTimeout(() => {\n      alert('Order placed successfully!');\n      navigate('/orders');\n    }, 2000);\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Please log in to checkout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/login'),\n          className: \"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700\",\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-8\",\n      children: \"Checkout\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-6\",\n          children: \"Shipping Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"First Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"firstName\",\n                required: true,\n                value: formData.firstName,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"lastName\",\n                required: true,\n                value: formData.lastName,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              required: true,\n              value: formData.email,\n              onChange: handleChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              name: \"phone\",\n              required: true,\n              value: formData.phone,\n              onChange: handleChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"address\",\n              required: true,\n              value: formData.address,\n              onChange: handleChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"City\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"city\",\n                required: true,\n                value: formData.city,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Postal Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"postalCode\",\n                value: formData.postalCode,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-semibold disabled:opacity-50\",\n            children: loading ? 'Processing...' : 'Place Order'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-6\",\n            children: \"Order Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Subtotal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"$89.99\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Shipping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Tax\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"$9.00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between font-bold text-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"$98.99\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-3 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/images/website/Kyo2.jpg\",\n                alt: \"Product\",\n                className: \"w-12 h-12 object-cover rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-sm\",\n                  children: \"Premium Anime Figure\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Qty: 1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: \"$89.99\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(Checkout, \"f6+s+hUeSupA0m4uEWtuxp98dEE=\", false, function () {\n  return [useNavigate];\n});\n_c = Checkout;\nexport default Checkout;\nvar _c;\n$RefreshReg$(_c, \"Checkout\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "Checkout", "user", "_s", "formData", "setFormData", "firstName", "lastName", "email", "phone", "address", "city", "postalCode", "loading", "setLoading", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "setTimeout", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "required", "onChange", "disabled", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/Checkout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst Checkout = ({ user }) => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: user?.email || '',\n    phone: '',\n    address: '',\n    city: '',\n    postalCode: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    \n    // Simulate order processing\n    setTimeout(() => {\n      alert('Order placed successfully!');\n      navigate('/orders');\n    }, 2000);\n  };\n\n  if (!user) {\n    return (\n      <div className=\"container py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Please log in to checkout</h1>\n          <button\n            onClick={() => navigate('/login')}\n            className=\"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700\"\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Checkout</h1>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Shipping Information */}\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 mb-6\">Shipping Information</h2>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  First Name\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"firstName\"\n                  required\n                  value={formData.firstName}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Last Name\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"lastName\"\n                  required\n                  value={formData.lastName}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                name=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Phone\n              </label>\n              <input\n                type=\"tel\"\n                name=\"phone\"\n                required\n                value={formData.phone}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Address\n              </label>\n              <input\n                type=\"text\"\n                name=\"address\"\n                required\n                value={formData.address}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              />\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  City\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"city\"\n                  required\n                  value={formData.city}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Postal Code\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"postalCode\"\n                  value={formData.postalCode}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n            </div>\n            \n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-semibold disabled:opacity-50\"\n            >\n              {loading ? 'Processing...' : 'Place Order'}\n            </button>\n          </form>\n        </div>\n\n        {/* Order Summary */}\n        <div>\n          <div className=\"bg-gray-50 rounded-lg p-6\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-6\">Order Summary</h2>\n            \n            <div className=\"space-y-4 mb-6\">\n              <div className=\"flex justify-between\">\n                <span>Subtotal</span>\n                <span>$89.99</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Shipping</span>\n                <span>Free</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Tax</span>\n                <span>$9.00</span>\n              </div>\n              <hr />\n              <div className=\"flex justify-between font-bold text-lg\">\n                <span>Total</span>\n                <span>$98.99</span>\n              </div>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 p-3 bg-white rounded-lg\">\n                <img\n                  src=\"/images/website/Kyo2.jpg\"\n                  alt=\"Product\"\n                  className=\"w-12 h-12 object-cover rounded\"\n                />\n                <div className=\"flex-1\">\n                  <p className=\"font-semibold text-sm\">Premium Anime Figure</p>\n                  <p className=\"text-gray-600 text-sm\">Qty: 1</p>\n                </div>\n                <span className=\"font-semibold\">$89.99</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Checkout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK,KAAI,EAAE;IACxBC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACAS,UAAU,CAAC,MAAM;MACfC,KAAK,CAAC,4BAA4B,CAAC;MACnCT,QAAQ,CAAC,SAAS,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAI,CAACb,IAAI,EAAE;IACT,oBACEF,OAAA;MAAKyB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B1B,OAAA;QAAKyB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1B,OAAA;UAAIyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF9B,OAAA;UACE+B,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,QAAQ,CAAE;UAClCU,SAAS,EAAC,6DAA6D;UAAAC,QAAA,EACxE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9B,OAAA;IAAKyB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B1B,OAAA;MAAIyB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEnE9B,OAAA;MAAKyB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD1B,OAAA;QAAA0B,QAAA,gBACE1B,OAAA;UAAIyB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE9E9B,OAAA;UAAMgC,QAAQ,EAAEX,YAAa;UAACI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjD1B,OAAA;YAAKyB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAOyB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9B,OAAA;gBACEiC,IAAI,EAAC,MAAM;gBACXd,IAAI,EAAC,WAAW;gBAChBe,QAAQ;gBACRd,KAAK,EAAEhB,QAAQ,CAACE,SAAU;gBAC1B6B,QAAQ,EAAEnB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAOyB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9B,OAAA;gBACEiC,IAAI,EAAC,MAAM;gBACXd,IAAI,EAAC,UAAU;gBACfe,QAAQ;gBACRd,KAAK,EAAEhB,QAAQ,CAACG,QAAS;gBACzB4B,QAAQ,EAAEnB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9B,OAAA;YAAA0B,QAAA,gBACE1B,OAAA;cAAOyB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9B,OAAA;cACEiC,IAAI,EAAC,OAAO;cACZd,IAAI,EAAC,OAAO;cACZe,QAAQ;cACRd,KAAK,EAAEhB,QAAQ,CAACI,KAAM;cACtB2B,QAAQ,EAAEnB,YAAa;cACvBS,SAAS,EAAC;YAAuG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9B,OAAA;YAAA0B,QAAA,gBACE1B,OAAA;cAAOyB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9B,OAAA;cACEiC,IAAI,EAAC,KAAK;cACVd,IAAI,EAAC,OAAO;cACZe,QAAQ;cACRd,KAAK,EAAEhB,QAAQ,CAACK,KAAM;cACtB0B,QAAQ,EAAEnB,YAAa;cACvBS,SAAS,EAAC;YAAuG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9B,OAAA;YAAA0B,QAAA,gBACE1B,OAAA;cAAOyB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9B,OAAA;cACEiC,IAAI,EAAC,MAAM;cACXd,IAAI,EAAC,SAAS;cACde,QAAQ;cACRd,KAAK,EAAEhB,QAAQ,CAACM,OAAQ;cACxByB,QAAQ,EAAEnB,YAAa;cACvBS,SAAS,EAAC;YAAuG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9B,OAAA;YAAKyB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAOyB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9B,OAAA;gBACEiC,IAAI,EAAC,MAAM;gBACXd,IAAI,EAAC,MAAM;gBACXe,QAAQ;gBACRd,KAAK,EAAEhB,QAAQ,CAACO,IAAK;gBACrBwB,QAAQ,EAAEnB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAOyB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9B,OAAA;gBACEiC,IAAI,EAAC,MAAM;gBACXd,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEhB,QAAQ,CAACQ,UAAW;gBAC3BuB,QAAQ,EAAEnB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9B,OAAA;YACEiC,IAAI,EAAC,QAAQ;YACbG,QAAQ,EAAEvB,OAAQ;YAClBY,SAAS,EAAC,wHAAwH;YAAAC,QAAA,EAEjIb,OAAO,GAAG,eAAe,GAAG;UAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9B,OAAA;QAAA0B,QAAA,eACE1B,OAAA;UAAKyB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC1B,OAAA;YAAIyB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEvE9B,OAAA;YAAKyB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B1B,OAAA;cAAKyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC1B,OAAA;gBAAA0B,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrB9B,OAAA;gBAAA0B,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACN9B,OAAA;cAAKyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC1B,OAAA;gBAAA0B,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrB9B,OAAA;gBAAA0B,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACN9B,OAAA;cAAKyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC1B,OAAA;gBAAA0B,QAAA,EAAM;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChB9B,OAAA;gBAAA0B,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACN9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA;cAAKyB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD1B,OAAA;gBAAA0B,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9B,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1B,OAAA;cAAKyB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClE1B,OAAA;gBACEqC,GAAG,EAAC,0BAA0B;gBAC9BC,GAAG,EAAC,SAAS;gBACbb,SAAS,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACF9B,OAAA;gBAAKyB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB1B,OAAA;kBAAGyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7D9B,OAAA;kBAAGyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN9B,OAAA;gBAAMyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAlNIF,QAAQ;EAAA,QAWKH,WAAW;AAAA;AAAAyC,EAAA,GAXxBtC,QAAQ;AAoNd,eAAeA,QAAQ;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}