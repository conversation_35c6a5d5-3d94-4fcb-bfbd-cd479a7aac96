<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getProduct($_GET['id']);
        } else {
            getProducts();
        }
        break;
    case 'POST':
        if ($action === 'upload-image') {
            uploadProductImage();
        } else {
            createProduct();
        }
        break;
    case 'PUT':
        if (isset($_GET['id'])) {
            updateProduct($_GET['id']);
        } else {
            sendResponse(['error' => 'Product ID required'], 400);
        }
        break;
    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteProduct($_GET['id']);
        } else {
            sendResponse(['error' => 'Product ID required'], 400);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getProducts() {
    global $mysqli;

    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 12);
    $offset = ($page - 1) * $limit;
    $category = $_GET['category'] ?? null;
    $search = $_GET['search'] ?? null;
    
    $whereClause = "WHERE 1=1";

    if ($category) {
        $category = escapeString($category);
        $whereClause .= " AND c.id = '$category'";
    }

    if ($search) {
        $search = escapeString($search);
        $whereClause .= " AND (p.name LIKE '%$search%' OR p.description LIKE '%$search%')";
    }

    $sql = "SELECT p.*, c.name as category_name,
                   GROUP_CONCAT(pi.image_path) as images
            FROM Products p
            LEFT JOIN Categories c ON p.category_id = c.id
            LEFT JOIN ProductImages pi ON p.id = pi.product_id
            $whereClause
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT $limit OFFSET $offset";

    $products = getAllRows($sql);
    
    // Get total count
    $countSql = "SELECT COUNT(DISTINCT p.id) as total
                 FROM Products p
                 LEFT JOIN Categories c ON p.category_id = c.id
                 $whereClause";
    $countResult = getSingleRow($countSql);
    $total = $countResult['total'];

    // Process images
    foreach ($products as &$product) {
        $product['images'] = $product['images'] ? explode(',', $product['images']) : [];
        $product['price'] = (float)$product['price'];
    }

    sendResponse([
        'products' => $products,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => (int)$total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function getProduct($id) {
    global $mysqli;

    $id = (int)$id;
    $sql = "SELECT p.*, c.name as category_name,
                   GROUP_CONCAT(pi.image_path) as images
            FROM Products p
            LEFT JOIN Categories c ON p.category_id = c.id
            LEFT JOIN ProductImages pi ON p.id = pi.product_id
            WHERE p.id = $id
            GROUP BY p.id";

    $product = getSingleRow($sql);

    if (!$product) {
        sendResponse(['error' => 'Product not found'], 404);
    }

    $product['images'] = $product['images'] ? explode(',', $product['images']) : [];
    $product['price'] = (float)$product['price'];

    sendResponse(['product' => $product]);
}

function uploadProductImage() {
    requireAdmin();

    if (!isset($_FILES['image'])) {
        sendResponse(['error' => 'No image file provided'], 400);
    }

    $file = $_FILES['image'];
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    if (!in_array($file['type'], $allowedTypes)) {
        sendResponse(['error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed'], 400);
    }

    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        sendResponse(['error' => 'File size too large. Maximum 5MB allowed'], 400);
    }

    // Create upload directory if it doesn't exist
    $uploadDir = '../uploads/products/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        $imagePath = '/uploads/products/' . $filename;
        sendResponse(['message' => 'Image uploaded successfully', 'image_path' => $imagePath], 201);
    } else {
        sendResponse(['error' => 'Failed to upload image'], 500);
    }
}

function createProduct() {
    requireAdmin();

    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['name', 'price', 'category_id']);

    global $mysqli;

    $name = escapeString($data['name']);
    $description = escapeString($data['description'] ?? '');
    $price = (float)$data['price'];
    $categoryId = (int)$data['category_id'];
    $stock = (int)($data['stock'] ?? 0);

    $insertQuery = "INSERT INTO Products (name, description, price, category_id, stock)
                    VALUES ('$name', '$description', $price, $categoryId, $stock)";
    executeQuery($insertQuery);

    $productId = mysqli_insert_id($mysqli);

    // Add images if provided
    if (isset($data['images']) && is_array($data['images'])) {
        foreach ($data['images'] as $imagePath) {
            $imagePath = escapeString($imagePath);
            $imageQuery = "INSERT INTO ProductImages (product_id, image_path) VALUES ($productId, '$imagePath')";
            executeQuery($imageQuery);
        }
    }

    sendResponse(['message' => 'Product created successfully', 'id' => $productId], 201);
}

function updateProduct($id) {
    requireAdmin();

    $data = json_decode(file_get_contents('php://input'), true);

    global $mysqli;

    $id = (int)$id;

    // Check if product exists
    $checkQuery = "SELECT id FROM Products WHERE id = $id";
    $existing = getSingleRow($checkQuery);
    if (!$existing) {
        sendResponse(['error' => 'Product not found'], 404);
    }

    $updates = [];

    if (isset($data['name'])) {
        $name = escapeString($data['name']);
        $updates[] = "name = '$name'";
    }
    if (isset($data['description'])) {
        $description = escapeString($data['description']);
        $updates[] = "description = '$description'";
    }
    if (isset($data['price'])) {
        $price = (float)$data['price'];
        $updates[] = "price = $price";
    }
    if (isset($data['category_id'])) {
        $categoryId = (int)$data['category_id'];
        $updates[] = "category_id = $categoryId";
    }
    if (isset($data['stock'])) {
        $stock = (int)$data['stock'];
        $updates[] = "stock = $stock";
    }

    if (!empty($updates)) {
        $updates[] = "updated_at = CURRENT_TIMESTAMP";
        $updateQuery = "UPDATE Products SET " . implode(', ', $updates) . " WHERE id = $id";
        executeQuery($updateQuery);
    }

    sendResponse(['message' => 'Product updated successfully']);
}

function deleteProduct($id) {
    requireAdmin();

    global $mysqli;

    $id = (int)$id;

    // Check if product exists
    $checkQuery = "SELECT id FROM Products WHERE id = $id";
    $existing = getSingleRow($checkQuery);
    if (!$existing) {
        sendResponse(['error' => 'Product not found'], 404);
    }

    // Delete product (images will be deleted by foreign key cascade)
    $deleteQuery = "DELETE FROM Products WHERE id = $id";
    executeQuery($deleteQuery);

    sendResponse(['message' => 'Product deleted successfully']);
}
?>
