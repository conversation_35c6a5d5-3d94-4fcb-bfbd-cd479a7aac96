<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getSettings();
        break;
    case 'PUT':
        updateSettings();
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getSettings() {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM StoreSettings LIMIT 1");
    $stmt->execute();
    $settings = $stmt->fetch();
    
    if (!$settings) {
        // Create default settings if none exist
        $stmt = $pdo->prepare("
            INSERT INTO StoreSettings (store_name, store_description) 
            VALUES ('KYOPAL', 'Your ultimate destination for premium anime merchandise and collectibles.')
        ");
        $stmt->execute();
        
        $stmt = $pdo->prepare("SELECT * FROM StoreSettings LIMIT 1");
        $stmt->execute();
        $settings = $stmt->fetch();
    }
    
    // Convert numeric fields
    $settings['shipping_fee'] = (float)$settings['shipping_fee'];
    $settings['free_shipping_threshold'] = (float)$settings['free_shipping_threshold'];
    $settings['tax_rate'] = (float)$settings['tax_rate'];
    
    sendResponse(['settings' => $settings]);
}

function updateSettings() {
    requireAdmin();
    
    $data = json_decode(file_get_contents('php://input'), true);
    
    global $pdo;
    
    // Check if settings exist
    $stmt = $pdo->prepare("SELECT id FROM StoreSettings LIMIT 1");
    $stmt->execute();
    $existing = $stmt->fetch();
    
    $fields = [];
    $params = [];
    
    $allowedFields = [
        'store_name', 'store_description', 'store_email', 'store_phone', 'store_address',
        'facebook_url', 'instagram_url', 'tiktok_url', 'whatsapp_url', 'telegram_url', 'x_url',
        'shipping_fee', 'free_shipping_threshold', 'tax_rate'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $fields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (!empty($fields)) {
        if ($existing) {
            // Update existing settings
            $fields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $existing['id'];
            
            $sql = "UPDATE StoreSettings SET " . implode(', ', $fields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
        } else {
            // Create new settings
            $fieldNames = [];
            $placeholders = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $fieldNames[] = $field;
                    $placeholders[] = '?';
                }
            }
            
            $sql = "INSERT INTO StoreSettings (" . implode(', ', $fieldNames) . ") VALUES (" . implode(', ', $placeholders) . ")";
            $stmt = $pdo->prepare($sql);
            $stmt->execute(array_values(array_intersect_key($data, array_flip($allowedFields))));
        }
    }
    
    sendResponse(['message' => 'Settings updated successfully']);
}
?>
