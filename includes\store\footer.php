<?php
$settings = mysqli_fetch_assoc(mysqli_query($mysqli, "SELECT * FROM StoreSettings LIMIT 1"));
?>
</main>
<footer class="bg-red-50 pt-12 pb-8 border-t border-gray-200">
    <div class="container">
        <div class="grid gap-y-8 gap-x-8 md:gap-x-24 lg:gap-x-48 md:grid-cols-2 lg:grid-cols-3">
            <div class="space-y-4">
                <div class="flex items-center gap-2">
                    <img src="<?= "images/" ?>website/logo.png" alt="KYOPAL" width="50" height="50" class="rounded-full" />
                    <h2 class="text-2xl font-bold text-gray-900">KYOPAL</h2>
                </div>
                <p class="text-gray-500">Your ultimate destination for premium anime merchandise and collectibles.</p>
                <div class="flex gap-4">
                    <?php if (!empty($settings['facebook_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['facebook_url']) ?>" class="footer-link" target="_blank">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook h-5 w-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
                        <span class="sr-only">Facebook</span>
                    </a>
                    <?php endif; ?>
                    <?php if (!empty($settings['instagram_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['instagram_url']) ?>" class="footer-link" target="_blank">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram h-5 w-5"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>
                        <span class="sr-only">Instagram</span>
                    </a>
                    <?php endif; ?>
                    <?php if (!empty($settings['tiktok_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['tiktok_url']) ?>" class="footer-link" target="_blank">TikTok</a>
                    <?php endif; ?>
                    <?php if (!empty($settings['whatsapp_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['whatsapp_url']) ?>" class="footer-link" target="_blank">WhatsApp</a>
                    <?php endif; ?>
                    <?php if (!empty($settings['telegram_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['telegram_url']) ?>" class="footer-link" target="_blank">Telegram</a>
                    <?php endif; ?>
                    <?php if (!empty($settings['x_url'])): ?>
                    <a href="<?= htmlspecialchars($settings['x_url']) ?>" class="footer-link" target="_blank">X</a>
                    <?php endif; ?>
                </div>
            </div>
            <div>
                <h3 class="font-bold text-lg mb-4 text-gray-900">Shop</h3>
                <ul class="space-y-2">
                    <li><a href="/products.php" class="footer-link">All Products</a></li>
                    <li><a href="/categories/figures" class="footer-link">Figures & Statues</a></li>
                    <li><a href="/categories/clothing" class="footer-link">Clothing & Apparel</a></li>
                    <li><a href="/categories/accessories" class="footer-link">Accessories</a></li>
                    <li><a href="/categories/manga" class="footer-link">Manga & Books</a></li>
                </ul>
            </div>
            <div>
                <h3 class="font-bold text-lg mb-4 text-gray-900">Customer Service</h3>
                <ul class="space-y-2">
                    <li><a href="/contact.php" class="footer-link">Contact Us</a></li>
                    <li><a href="/faq.php" class="footer-link">FAQ</a></li>
                    <li><a href="/terms.php" class="footer-link">Terms & Conditions</a></li>
                    <li><a href="/privacy.php" class="footer-link">Privacy Policy</a></li>
                </ul>
            </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-center text-gray-500">&copy;<?= date('Y') ?> KYOPAL. All rights reserved.</p>
        </div>
    </div>
</footer>
<script>
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileSearchBtn = document.getElementById('mobileSearchBtn');
    const mobileSearch = document.getElementById('mobileSearch');

    function toggleElement(element, isOpen) {
        if (isOpen) {
            element.classList.remove('max-h-0', 'opacity-0');
            element.classList.add(element === mobileMenu ? 'max-h-[500px]' : 'max-h-24', 'opacity-100');
        } else {
            element.classList.add('max-h-0', 'opacity-0');
            element.classList.remove(element === mobileMenu ? 'max-h-[500px]' : 'max-h-24', 'opacity-100');
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        toggleElement(mobileMenu, false);
        toggleElement(mobileSearch, false);
    });

    mobileMenuBtn.addEventListener('click', () => {
        const isMenuClosed = mobileMenu.classList.contains('max-h-0');
        toggleElement(mobileMenu, isMenuClosed);
        toggleElement(mobileSearch, false);
    });

    mobileSearchBtn.addEventListener('click', () => {
        toggleElement(mobileSearch, mobileSearch.classList.contains('max-h-0'));
        toggleElement(mobileMenu, false);
    });
</script>
</body>
</html>