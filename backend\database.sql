-- Create database
CREATE DATABASE IF NOT EXISTS kyopal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kyopal;

-- Users table
CREATE TABLE Users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE Categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE Products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category_id INT,
    stock INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES Categories(id) ON DELETE SET NULL
);

-- Product Images table
CREATE TABLE ProductImages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    image_path VARCHAR(500) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES Products(id) ON DELETE CASCADE
);

-- Orders table
CREATE TABLE Orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    status ENUM('cart', 'pending', 'shipped', 'delivered', 'cancelled') DEFAULT 'cart',
    total DECIMAL(10, 2) DEFAULT 0,
    shipping_address TEXT,
    billing_address TEXT,
    payment_method VARCHAR(100),
    payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE SET NULL
);

-- Order Items table
CREATE TABLE OrderItems (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES Orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES Products(id) ON DELETE CASCADE
);

-- Favorites table
CREATE TABLE Favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES Products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, product_id)
);

-- Contact Messages table
CREATE TABLE ContactMessages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Store Settings table
CREATE TABLE StoreSettings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    store_name VARCHAR(255) DEFAULT 'KYOPAL',
    store_description TEXT,
    store_email VARCHAR(255),
    store_phone VARCHAR(100),
    store_address TEXT,
    facebook_url VARCHAR(500),
    instagram_url VARCHAR(500),
    tiktok_url VARCHAR(500),
    whatsapp_url VARCHAR(500),
    telegram_url VARCHAR(500),
    x_url VARCHAR(500),
    shipping_fee DECIMAL(10, 2) DEFAULT 5.00,
    free_shipping_threshold DECIMAL(10, 2) DEFAULT 50.00,
    tax_rate DECIMAL(5, 2) DEFAULT 10.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO Users (name, email, password, role) VALUES 
('Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert default categories
INSERT INTO Categories (name, image_path) VALUES 
('Figures', '/images/categories/figures.jpg'),
('Clothing', '/images/categories/clothing.jpg'),
('Accessories', '/images/categories/accessories.jpg'),
('Manga', '/images/categories/manga.jpg'),
('Posters', '/images/categories/posters.jpg'),
('Keychains', '/images/categories/keychains.jpg'),
('Plushies', '/images/categories/plushies.jpg'),
('Collectibles', '/images/categories/collectibles.jpg');

-- Insert sample products
INSERT INTO Products (name, description, price, category_id, stock) VALUES 
('Premium Anime Figure', 'High-quality anime figure with detailed craftsmanship', 89.99, 1, 25),
('Anime Character T-Shirt', 'Comfortable cotton t-shirt with anime character design', 24.99, 2, 50),
('Character Keychain Set', 'Set of 3 keychains featuring popular anime characters', 12.99, 6, 100),
('Manga Volume Collection', 'Complete volume set of popular manga series', 45.99, 4, 30),
('Anime Wall Poster', 'High-quality poster featuring stunning anime artwork', 19.99, 5, 75);

-- Insert default store settings
INSERT INTO StoreSettings (
    store_name, 
    store_description, 
    store_email, 
    store_phone, 
    store_address,
    facebook_url,
    instagram_url,
    shipping_fee,
    free_shipping_threshold,
    tax_rate
) VALUES (
    'KYOPAL',
    'Your ultimate destination for premium anime merchandise and collectibles.',
    '<EMAIL>',
    '+970 XXX XXXX',
    'Palestine',
    'https://facebook.com/kyopal',
    'https://instagram.com/kyopal',
    5.00,
    50.00,
    10.00
);
