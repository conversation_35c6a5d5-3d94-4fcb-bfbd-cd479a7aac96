<?php
$title = "Reset Password";
include_once(__DIR__ . '/../includes/store/header.php');
?>
<section class="container flex items-center justify-center py-16">
    <form id="reset-password-form"
        class="bg-white rounded-lg shadow-lg p-6 space-y-4 border border-red-200 hover:shadow-xl transition duration-300 w-full max-w-md"
        aria-label="Reset Password Form" method="POST" action="reset_password.php" onsubmit="return validateForm()">
        <div class="text-center space-y-2">
            <img src="assets/images/logo.png" height="70" width="70" alt="" class="mx-auto">
            <h2 class="text-xl font-semibold">Reset Your Password</h2>
            <p class="text-sm text-gray-500">Enter your new password below</p>
        </div>

        <input type="hidden" id="token" name="token"
            value="<?= isset($_GET['token']) ? htmlspecialchars($_GET['token']) : '' ?>">
        <input type="hidden" id="email" name="email"
            value="<?= isset($_GET['email']) ? htmlspecialchars($_GET['email']) : '' ?>">

        <div>
            <label for="password" class="block text-sm font-medium text-gray-700">New Password</label>
            <input type="password" id="password" name="password" placeholder="Enter your new password"
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
            <p id="password-error" class="text-red-500 text-sm hidden">Password is required</p>
        </div>

        <div>
            <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
            <input type="password" id="confirm-password" name="confirm-password" placeholder="Confirm your new password"
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
            <p id="confirm-password-error" class="text-red-500 text-sm hidden">Passwords do not match</p>
        </div>

        <div class="pt-2">
            <button type="submit"
                class="bg-red-600 text-white w-full py-2 px-6 rounded-md hover:bg-red-700 transition">Reset
                Password</button>
        </div>

        <div class="text-center">
            <p class="text-sm text-gray-500">
                <a href="/login.php" class="text-red-600 hover:underline">Back to Login</a>
            </p>
        </div>
    </form>
</section>

<script>
    function validateForm() {
        document.getElementById('password-error').classList.add('hidden');
        document.getElementById('confirm-password-error').classList.add('hidden');

        let password = document.getElementById('password').value;
        let confirmPassword = document.getElementById('confirm-password').value;

        let valid = true;

        if (password.trim() === "") {
            document.getElementById('password-error').classList.remove('hidden');
            document.getElementById('password-error').innerText = "Password is required";
            valid = false;
        } else if (password.length < 8) {
            document.getElementById('password-error').classList.remove('hidden');
            document.getElementById('password-error').innerText = "Password must be at least 8 characters";
            valid = false;
        }

        if (password !== confirmPassword) {
            document.getElementById('confirm-password-error').classList.remove('hidden');
            valid = false;
        }

        return valid;
    }
</script>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>