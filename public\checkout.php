<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
if (!isset($_SESSION['user'])) {
    header('Location: /login.php');
    exit;
}
include_once(__DIR__ . '/../includes/store/header.php');
$title = "Checkout";

$cartItems = [];
$subtotal = 0;
$shipping = 5.99;
$taxRate = 0.05;
$userId = $_SESSION['user']['id'];
$cartQuery = mysqli_query($mysqli, "SELECT oi.*, p.name, p.price, p.category_id, (SELECT image_path FROM ProductImages WHERE product_id = p.id LIMIT 1) as img FROM OrderItems oi JOIN Products p ON oi.product_id = p.id JOIN Orders o ON oi.order_id = o.id WHERE o.user_id = $userId AND o.status = 'cart'");
while ($row = mysqli_fetch_assoc($cartQuery)) {
    $cartItems[] = $row;
    $subtotal += $row['price'] * $row['quantity'];
}
$tax = $subtotal * $taxRate;
$total = $subtotal + $shipping + $tax;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $userId = $_SESSION['user']['id'];
    $address = mysqli_real_escape_string($mysqli, $_POST['address'] ?? '');
    $city = mysqli_real_escape_string($mysqli, $_POST['city'] ?? '');
    $country = mysqli_real_escape_string($mysqli, $_POST['country'] ?? '');
    $postal = mysqli_real_escape_string($mysqli, $_POST['postal_code'] ?? '');
    $notes = mysqli_real_escape_string($mysqli, $_POST['shipping_notes'] ?? '');
    $shipping_address = "$address, $city, $country, $postal";
    $orderRes = mysqli_query($mysqli, "SELECT id FROM Orders WHERE user_id = $userId AND status = 'cart' LIMIT 1");
    if ($order = mysqli_fetch_assoc($orderRes)) {
        $orderId = $order['id'];
        // Update order info
        mysqli_query($mysqli, "UPDATE Orders SET status = 'pending', shipping_address = '$shipping_address', updated_at = CURRENT_TIMESTAMP WHERE id = $orderId");
        // Calculate total
        $itemsRes = mysqli_query($mysqli, "SELECT SUM(quantity * price) as total FROM OrderItems WHERE order_id = $orderId");
        $total = (float)mysqli_fetch_assoc($itemsRes)['total'];
        $shipping = 5.99;
        $tax = $total * 0.05;
        $grandTotal = $total + $shipping + $tax;
        mysqli_query($mysqli, "UPDATE Orders SET total_price = $grandTotal WHERE id = $orderId");
        // Create new empty cart for user
        mysqli_query($mysqli, "INSERT INTO Orders (user_id, status, total_price) VALUES ($userId, 'cart', 0)");
        header('Location: /orders.php');
        exit;
    }
}
?>

<section class="container py-10">
    <div class="flex flex-col space-y-6">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold">Checkout</h1>
            <p class="text-gray-600 mt-2">Complete your order</p>
        </div>

        <?php if (empty($cartItems)): ?>
            <div class="py-10 text-center">
                <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <h2 class="mt-4 text-lg font-medium">Your cart is empty</h2>
                <p class="mt-2 text-gray-500">Add some items to your cart to proceed with checkout</p>
                <a href="/products.php"
                    class="mt-4 inline-block bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition">
                    Browse Products
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <form id="checkout-form" class="space-y-8">
                        <div class="bg-white rounded-lg shadow-md p-6 border border-red-50">
                            <h2 class="text-xl font-bold mb-4">Contact Information</h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700">First
                                        Name</label>
                                    <input type="text" id="first_name" name="first_name"
                                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                        required />
                                </div>
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                                    <input type="text" id="last_name" name="last_name"
                                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                        required />
                                </div>
                            </div>

                            <div class="mt-4">
                                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" id="email" name="email"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                    required />
                            </div>

                            <div class="mt-4">
                                <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                <input type="tel" id="phone" name="phone"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                    required />
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6  border border-red-50">
                            <h2 class="text-xl font-bold mb-4">Shipping Information</h2>

                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                <input type="text" id="address" name="address"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                    required />
                            </div>

                            <div class="mt-4">
                                <label for="apartment" class="block text-sm font-medium text-gray-700">Apartment, suite,
                                    etc. (optional)</label>
                                <input type="text" id="apartment" name="apartment"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                <div>
                                    <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                                    <input type="text" id="city" name="city"
                                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                        required />
                                </div>
                                <div>
                                    <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
                                    <select id="country" name="country"
                                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                        required>
                                        <option value="">Select Country</option>
                                        <option value="PS">Palestine</option>
                                        <option value="JO">Jordan</option>
                                        <option value="LB">Lebanon</option>
                                        <option value="EG">Egypt</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal
                                        Code</label>
                                    <input type="text" id="postal_code" name="postal_code"
                                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"
                                        required />
                                </div>
                            </div>

                            <div class="mt-4">
                                <label for="shipping_notes" class="block text-sm font-medium text-gray-700">Delivery
                                    Instructions (optional)</label>
                                <textarea id="shipping_notes" name="shipping_notes" rows="2"
                                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400"></textarea>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6  border border-red-50">
                            <h2 class="text-xl font-bold mb-4">Payment Method</h2>

                            <div class="space-y-4">
                                <div class="border-t pt-4">
                                    <div class="flex items-center">
                                        <input type="radio" id="payment_cod" name="payment_method" value="cod"
                                            class="h-4 w-4 accent-red-600" />
                                        <label for="payment_cod" class="ml-2 text-gray-700">Cash on Delivery</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-md p-6 sticky top-20  border border-red-50">
                        <h2 class="text-xl font-bold mb-4">Order Summary</h2>

                        <div class="divide-y">
                            <?php foreach ($cartItems as $item): ?>
                                <div class="py-4 flex">
                                    <div class="flex-shrink-0 mr-4 relative">
                                        <img src="<?= $item['img'] ?>" alt="<?= htmlspecialchars($item['name']) ?>"
                                            class="w-16 h-16 object-cover rounded-md">
                                        <span
                                            class="absolute -top-2 -right-2 bg-red-600 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                                            <?= $item['quantity'] ?>
                                        </span>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-sm font-medium"><?= htmlspecialchars($item['name']) ?></h3>
                                        <p class="text-xs text-gray-500"><?= htmlspecialchars($item['category']) ?></p>
                                    </div>
                                    <div class="ml-4 text-sm font-medium">
                                        $<?= number_format($item['price'] * $item['quantity'], 2) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="mt-4 pt-4 border-t">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">Subtotal</span>
                                <span>$<?= number_format($subtotal, 2) ?></span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">Shipping</span>
                                <span>$<?= number_format($shipping, 2) ?></span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">Tax (5%)</span>
                                <span>$<?= number_format($tax, 2) ?></span>
                            </div>
                            <div class="flex justify-between items-center pt-4 border-t mt-4">
                                <span class="font-bold">Total</span>
                                <span class="font-bold text-lg">$<?= number_format($total, 2) ?></span>
                            </div>
                        </div>

                        <div class="mt-6">
                            <button type="submit" form="checkout-form"
                                class="bg-red-600 text-white py-3 px-6 rounded-md hover:bg-red-700 transition w-full font-bold">
                                Place Order
                            </button>
                        </div>


                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
    document.getElementById('checkout-form')?.addEventListener('submit', function (e) {
        e.preventDefault();
        alert('This is a demo - no actual order will be placed.');
    });
</script>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>