<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first = trim($_POST['firstname'] ?? '');
    $last = trim($_POST['lastname'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm = $_POST['confirm_password'] ?? '';
    if ($first && $last && $email && $password && $confirm) {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Invalid email address.';
        } elseif ($password !== $confirm) {
            $error = 'Passwords do not match.';
        } else {
            $stmt = mysqli_prepare($mysqli, "SELECT id FROM Users WHERE email = ? LIMIT 1");
            mysqli_stmt_bind_param($stmt, 's', $email);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_store_result($stmt);
            if (mysqli_stmt_num_rows($stmt) > 0) {
                $error = 'Email already registered.';
            } else {
                $name = $first . ' ' . $last;
                $hash = password_hash($password, PASSWORD_DEFAULT);
                $stmt = mysqli_prepare($mysqli, "INSERT INTO Users (name, email, password) VALUES (?, ?, ?)");
                mysqli_stmt_bind_param($stmt, 'sss', $name, $email, $hash);
                if (mysqli_stmt_execute($stmt)) {
                    $user_id = mysqli_insert_id($mysqli);
                    $_SESSION['user'] = [
                        'id' => $user_id,
                        'name' => $name,
                        'email' => $email,
                        'role' => 'user'
                    ];
                    // Cart sync logic
                    if (!empty($_POST['cart_data'])) {
                        $cartData = json_decode($_POST['cart_data'], true);
                        if ($cartData && is_array($cartData)) {
                            $orderRes = mysqli_query($mysqli, "SELECT id FROM Orders WHERE user_id = $user_id AND status = 'cart' LIMIT 1");
                            if ($order = mysqli_fetch_assoc($orderRes)) {
                                $orderId = $order['id'];
                            } else {
                                mysqli_query($mysqli, "INSERT INTO Orders (user_id, status, total_price) VALUES ($user_id, 'cart', 0)");
                                $orderId = mysqli_insert_id($mysqli);
                            }
                            foreach ($cartData as $item) {
                                $pid = (int)($item['id'] ?? 0);
                                $qty = (int)($item['quantity'] ?? 1);
                                $price = (float)($item['price'] ?? 0);
                                if ($pid > 0 && $qty > 0) {
                                    $exists = mysqli_query($mysqli, "SELECT id FROM OrderItems WHERE order_id = $orderId AND product_id = $pid");
                                    if (mysqli_num_rows($exists)) {
                                        mysqli_query($mysqli, "UPDATE OrderItems SET quantity = quantity + $qty WHERE order_id = $orderId AND product_id = $pid");
                                    } else {
                                        mysqli_query($mysqli, "INSERT INTO OrderItems (order_id, product_id, quantity, price) VALUES ($orderId, $pid, $qty, $price)");
                                    }
                                }
                            }
                        }
                    }
                    header('Location: /');
                    exit;
                } else {
                    $error = 'Registration failed. Please try again.';
                }
            }
        }
    } else {
        $error = 'Please fill in all fields.';
    }
}

$title = "Register";
include_once(__DIR__ . '/../includes/store/header.php');
?>
<?php if (!empty(
    $error)): ?>
    <div class="bg-red-100 text-red-700 px-4 py-2 rounded mb-4 text-center"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>
<section class="container flex items-center justify-center py-10">
    <form id="register-form" class="bg-white rounded-lg shadow-lg p-6 space-y-4 border border-red-200 hover:shadow-xl transition duration-300 w-full max-w-md" method="POST" action="register.php" onsubmit="return validateForm()">
        <div class="text-center space-y-2">
            <img src="<?= "images/" ?>website/logo.png" height="70" width="70" alt=""  class="mx-auto">
            <h2 class="text-xl font-semibold">Create an Account</h2>
            <p class="text-sm text-gray-500">Enter your information to create an account.</p>
        </div>

        <div class="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
            <div class="w-full">
                <label for="firstname" class="block text-sm font-medium text-gray-700">First Name</label>
                <input type="text" id="firstname" name="firstname" placeholder="Enter your first name"
                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400 text-sm" />
                <p id="firstname-error" class="text-red-500 text-sm hidden">First name is required</p>
            </div>
            <div class="w-full">
                <label for="lastname" class="block text-sm font-medium text-gray-700">Last Name</label>
                <input type="text" id="lastname" name="lastname" placeholder="Enter your last name"
                    class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400 text-sm" />
                <p id="lastname-error" class="text-red-500 text-sm hidden">Last name is required</p>
            </div>
        </div>
        <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" id="email" name="email" placeholder="Enter your email"
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400 text-sm" />
            <p id="email-error" class="text-red-500 text-sm hidden">Email is required</p>
        </div>

        <div>
            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
            <input type="password" id="password" name="password" placeholder="Enter your password"
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400 text-sm" />
            <p id="password-error" class="text-red-500 text-sm hidden">Password is required</p>
        </div>

        <div>
            <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm
                Password</label>
            <input type="password" id="confirm-password" name="confirm_password"
                placeholder="Confirm your password"
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400 text-sm" />
            <p id="confirm-password-error" class="text-red-500 text-sm hidden">Passwords do not match</p>
        </div>

        <div class="flex items-center">
            <input type="checkbox" id="terms" name="terms" class="h-4 w-4 accent-red-600" />
            <label for="terms" class="ml-2 text-xs text-gray-500">I agree to the <a href="terms.php"
                    class="text-red-600 hover:underline">Terms and Conditions </a>and <a href="privacy.php"
                    class="text-red-600 hover:underline">Privacy Policy</a></label>
        </div>

        <div>
            <button type="submit"
                class="bg-red-600 text-white w-full py-2 px-6 rounded-md hover:bg-red-700 transition">Register</button>
        </div>

        <div class="text-center">
            <p class="text-sm text-gray-500">Already have an account?
                <a href="/login.php" class="text-red-600 hover:underline">Login</a>
            </p>
        </div>
    </form>
</section>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>
<script>
    function validateForm() {
        document.getElementById('firstname-error').classList.add('hidden');
        document.getElementById('lastname-error').classList.add('hidden');
        document.getElementById('password-error').classList.add('hidden');

        let firstname = document.getElementById('firstname').value.trim();
        let lastname = document.getElementById('lastname').value.trim();
        let password = document.getElementById('password').value.trim();

        let valid = true;

        if (firstname === "") {
            document.getElementById('firstname-error').classList.remove('hidden');
            valid = false;
        }

        if (lastname === "") {
            document.getElementById('lastname-error').classList.remove('hidden');
            valid = false;
        }

        if (password === "") {
            document.getElementById('password-error').classList.remove('hidden');
            valid = false;
        }

        return valid;
    }

    // On register form submit, if cart exists in localStorage, add it to a hidden field
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            var cart = localStorage.getItem('cart');
            if (cart) {
                let input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'cart_data';
                input.value = cart;
                registerForm.appendChild(input);
            }
        });
    }
</script>
