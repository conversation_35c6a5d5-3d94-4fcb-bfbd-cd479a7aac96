<div class="bg-white rounded-lg shadow-md p-6 h-fit border border-red-50 lg:sticky top-26">
    <nav class="space-y-1">
        <a href="/account.php"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-md <?php if($requestedPage == "account") echo 'text-red-600 bg-red-50'?>">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>Profile</span>
        </a>
        <a href="/orders.php"
           class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-md <?php if($requestedPage == "orders") echo 'text-red-600 bg-red-50'?>">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <span>Orders</span>
        </a>
        <a href="/favorites.php"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-md <?php if($requestedPage == "favorites") echo 'text-red-600 bg-red-50'?>">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                </path>
            </svg>
            <span>Favorites</span>
        </a>
        <a href="/logout.php" class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-md <?php if($requestedPage == "logout") echo 'text-red-600 bg-red-50'?>">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                </path>
            </svg>
            <span>Logout</span>
        </a>
    </nav>
</div>