{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\components\\\\AdminSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = ({\n  user\n}) => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const location = useLocation();\n  const isActive = path => location.pathname === path;\n  const menuItems = [{\n    path: '/admin',\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/products',\n    name: 'Products',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/categories',\n    name: 'Categories',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/orders',\n    name: 'Orders',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/users',\n    name: 'Users',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/messages',\n    name: 'Messages',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this)\n  }, {\n    path: '/admin/settings',\n    name: 'Settings',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"h-5 w-5 mr-3\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this)\n  }];\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n      className: \"lg:hidden fixed top-4 left-4 z-50 bg-gray-800 text-white p-2 rounded-md\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"h-6 w-6\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 6h16M4 12h16M4 18h16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/30 z-30 lg:hidden\",\n      onClick: () => setIsMobileMenuOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-gray-800 text-white w-64 min-h-screen flex flex-col fixed z-40 left-0 top-0 transform transition-transform duration-300 ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/website/logo.png\",\n            alt: \"KyoPal Logo\",\n            className: \"h-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl font-bold\",\n              children: \"KyoPal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Admin Panel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-1\",\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: `flex items-center px-4 py-3 transition-colors ${isActive(item.path) ? 'bg-red-600 text-white hover:bg-red-700' : 'text-gray-300 hover:bg-gray-700'}`,\n              onClick: () => setIsMobileMenuOpen(false),\n              children: [item.icon, item.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-700 text-sm text-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"block hover:text-white mb-2 inline-flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4 inline mr-1\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), \"Back to Store\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"KyoPal Admin v1.0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AdminSidebar, \"bzL1RznBT91J0BRiFq6XQSqndG8=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminSidebar", "user", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "location", "isActive", "path", "pathname", "menuItems", "name", "icon", "className", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onClick", "to", "src", "alt", "map", "item", "xmlns", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/components/AdminSidebar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst AdminSidebar = ({ user }) => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const location = useLocation();\n\n  const isActive = (path) => location.pathname === path;\n\n  const menuItems = [\n    {\n      path: '/admin',\n      name: 'Dashboard',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/products',\n      name: 'Products',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/categories',\n      name: 'Categories',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/orders',\n      name: 'Orders',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/users',\n      name: 'Users',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/messages',\n      name: 'Messages',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      )\n    },\n    {\n      path: '/admin/settings',\n      name: 'Settings',\n      icon: (\n        <svg className=\"h-5 w-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n        </svg>\n      )\n    }\n  ];\n\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <button\n        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n        className=\"lg:hidden fixed top-4 left-4 z-50 bg-gray-800 text-white p-2 rounded-md\"\n      >\n        <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n        </svg>\n      </button>\n\n      {/* Overlay */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black/30 z-30 lg:hidden\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        ></div>\n      )}\n\n      {/* Sidebar */}\n      <div className={`bg-gray-800 text-white w-64 min-h-screen flex flex-col fixed z-40 left-0 top-0 transform transition-transform duration-300 ${\n        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'\n      } lg:translate-x-0`}>\n        \n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-700\">\n          <Link to=\"/\" className=\"flex items-center\">\n            <img src=\"/images/website/logo.png\" alt=\"KyoPal Logo\" className=\"h-8\" />\n            <div className=\"ml-2\">\n              <div className=\"text-xl font-bold\">KyoPal</div>\n              <div className=\"text-xs text-gray-400\">Admin Panel</div>\n            </div>\n          </Link>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 py-4\">\n          <ul className=\"space-y-1\">\n            {menuItems.map((item) => (\n              <li key={item.path}>\n                <Link\n                  to={item.path}\n                  className={`flex items-center px-4 py-3 transition-colors ${\n                    isActive(item.path)\n                      ? 'bg-red-600 text-white hover:bg-red-700'\n                      : 'text-gray-300 hover:bg-gray-700'\n                  }`}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.icon}\n                  {item.name}\n                </Link>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-gray-700 text-sm text-gray-400\">\n          <Link to=\"/\" className=\"block hover:text-white mb-2 inline-flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 inline mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n            </svg>\n            Back to Store\n          </Link>\n          <div>KyoPal Admin v1.0</div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACjC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMY,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,QAAQ,GAAIC,IAAI,IAAKF,QAAQ,CAACG,QAAQ,KAAKD,IAAI;EAErD,MAAME,SAAS,GAAG,CAChB;IACEF,IAAI,EAAE,QAAQ;IACdG,IAAI,EAAE,WAAW;IACjBC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAkJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvN;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,iBAAiB;IACvBG,IAAI,EAAE,UAAU;IAChBC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAmF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxJ;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,mBAAmB;IACzBG,IAAI,EAAE,YAAY;IAClBC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAkM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvQ;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,eAAe;IACrBG,IAAI,EAAE,QAAQ;IACdC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAiK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtO;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,cAAc;IACpBG,IAAI,EAAE,OAAO;IACbC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAA+G;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpL;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,iBAAiB;IACvBG,IAAI,EAAE,UAAU;IAChBC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAsG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3K;EAET,CAAC,EACD;IACEjB,IAAI,EAAE,iBAAiB;IACvBG,IAAI,EAAE,UAAU;IAChBC,IAAI,eACFd,OAAA;MAAKe,SAAS,EAAC,cAAc;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eACjFnB,OAAA;QAAMoB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAC,GAAG;QAACC,CAAC,EAAC;MAAqe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1iB;EAET,CAAC,CACF;EAED,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,OAAO,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,oBACE5B,OAAA,CAAAE,SAAA;IAAAiB,QAAA,gBAEEnB,OAAA;MACE6B,OAAO,EAAEA,CAAA,KAAMtB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;MACtDS,SAAS,EAAC,yEAAyE;MAAAI,QAAA,eAEnFnB,OAAA;QAAKe,SAAS,EAAC,SAAS;QAACC,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAC,QAAA,eAC5EnB,OAAA;UAAMoB,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAC,GAAG;UAACC,CAAC,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRrB,gBAAgB,iBACfN,OAAA;MACEe,SAAS,EAAC,0CAA0C;MACpDc,OAAO,EAAEA,CAAA,KAAMtB,mBAAmB,CAAC,KAAK;IAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACP,eAGD3B,OAAA;MAAKe,SAAS,EAAE,8HACdT,gBAAgB,GAAG,eAAe,GAAG,mBAAmB,mBACtC;MAAAa,QAAA,gBAGlBnB,OAAA;QAAKe,SAAS,EAAC,8BAA8B;QAAAI,QAAA,eAC3CnB,OAAA,CAACH,IAAI;UAACiC,EAAE,EAAC,GAAG;UAACf,SAAS,EAAC,mBAAmB;UAAAI,QAAA,gBACxCnB,OAAA;YAAK+B,GAAG,EAAC,0BAA0B;YAACC,GAAG,EAAC,aAAa;YAACjB,SAAS,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxE3B,OAAA;YAAKe,SAAS,EAAC,MAAM;YAAAI,QAAA,gBACnBnB,OAAA;cAAKe,SAAS,EAAC,mBAAmB;cAAAI,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/C3B,OAAA;cAAKe,SAAS,EAAC,uBAAuB;cAAAI,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3B,OAAA;QAAKe,SAAS,EAAC,aAAa;QAAAI,QAAA,eAC1BnB,OAAA;UAAIe,SAAS,EAAC,WAAW;UAAAI,QAAA,EACtBP,SAAS,CAACqB,GAAG,CAAEC,IAAI,iBAClBlC,OAAA;YAAAmB,QAAA,eACEnB,OAAA,CAACH,IAAI;cACHiC,EAAE,EAAEI,IAAI,CAACxB,IAAK;cACdK,SAAS,EAAE,iDACTN,QAAQ,CAACyB,IAAI,CAACxB,IAAI,CAAC,GACf,wCAAwC,GACxC,iCAAiC,EACpC;cACHmB,OAAO,EAAEA,CAAA,KAAMtB,mBAAmB,CAAC,KAAK,CAAE;cAAAY,QAAA,GAEzCe,IAAI,CAACpB,IAAI,EACToB,IAAI,CAACrB,IAAI;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC,GAZAO,IAAI,CAACxB,IAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAad,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGN3B,OAAA;QAAKe,SAAS,EAAC,oDAAoD;QAAAI,QAAA,gBACjEnB,OAAA,CAACH,IAAI;UAACiC,EAAE,EAAC,GAAG;UAACf,SAAS,EAAC,sDAAsD;UAAAI,QAAA,gBAC3EnB,OAAA;YAAKmC,KAAK,EAAC,4BAA4B;YAACpB,SAAS,EAAC,qBAAqB;YAACC,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAAAE,QAAA,eAC3HnB,OAAA;cAAMoB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,iBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP3B,OAAA;UAAAmB,QAAA,EAAK;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACtB,EAAA,CAnJIF,YAAY;EAAA,QAECL,WAAW;AAAA;AAAAsC,EAAA,GAFxBjC,YAAY;AAqJlB,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}