{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\components\\\\AdminLayout.js\";\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport AdminSidebar from './AdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLayout = ({\n  user,\n  children\n}) => {\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 12\n    }, this);\n  }\n  if (user.role !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"You don't have permission to access this area.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex min-h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      user: user\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 transition-all duration-300 lg:ml-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"bg-white shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center px-6 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-semibold text-gray-800\",\n            children: \"Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Welcome, \", user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"p-6\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "Navigate", "AdminSidebar", "jsxDEV", "_jsxDEV", "AdminLayout", "user", "children", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "className", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/components/AdminLayout.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport AdminSidebar from './AdminSidebar';\n\nconst AdminLayout = ({ user, children }) => {\n  if (!user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (user.role !== 'admin') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Access Denied</h1>\n          <p className=\"text-gray-600\">You don't have permission to access this area.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex min-h-screen bg-gray-100\">\n      <AdminSidebar user={user} />\n      \n      <div className=\"flex-1 transition-all duration-300 lg:ml-64\">\n        <header className=\"bg-white shadow-sm\">\n          <div className=\"flex justify-between items-center px-6 py-3\">\n            <h1 className=\"text-2xl font-semibold text-gray-800\">Admin Dashboard</h1>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">Welcome, {user.name}</span>\n            </div>\n          </div>\n        </header>\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAC1C,IAAI,CAACD,IAAI,EAAE;IACT,oBAAOF,OAAA,CAACH,QAAQ;MAACO,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,IAAIP,IAAI,CAACQ,IAAI,KAAK,OAAO,EAAE;IACzB,oBACEV,OAAA;MAAKW,SAAS,EAAC,+CAA+C;MAAAR,QAAA,eAC5DH,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAR,QAAA,gBAC1BH,OAAA;UAAIW,SAAS,EAAC,uCAAuC;UAAAR,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxET,OAAA;UAAGW,SAAS,EAAC,eAAe;UAAAR,QAAA,EAAC;QAA8C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACET,OAAA;IAAKW,SAAS,EAAC,+BAA+B;IAAAR,QAAA,gBAC5CH,OAAA,CAACF,YAAY;MAACI,IAAI,EAAEA;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE5BT,OAAA;MAAKW,SAAS,EAAC,6CAA6C;MAAAR,QAAA,gBAC1DH,OAAA;QAAQW,SAAS,EAAC,oBAAoB;QAAAR,QAAA,eACpCH,OAAA;UAAKW,SAAS,EAAC,6CAA6C;UAAAR,QAAA,gBAC1DH,OAAA;YAAIW,SAAS,EAAC,sCAAsC;YAAAR,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzET,OAAA;YAAKW,SAAS,EAAC,6BAA6B;YAAAR,QAAA,eAC1CH,OAAA;cAAMW,SAAS,EAAC,uBAAuB;cAAAR,QAAA,GAAC,WAAS,EAACD,IAAI,CAACU,IAAI;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETT,OAAA;QAAMW,SAAS,EAAC,KAAK;QAAAR,QAAA,EAClBA;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GApCIZ,WAAW;AAsCjB,eAAeA,WAAW;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}