<?php
$title = "Product Details";
include_once(__DIR__ . '/../includes/store/header.php');
$product = [
    'id' => 1,
    'name' => 'Anime Character Figure',
    'category' => 'Figures',
    'price' => 49.99,
    'old_price' => 59.99,
    'description' => 'High-quality collectible figure of a popular anime character. Made with premium materials and attention to detail.',
    'stock' => 5,
    'rating' => 4.5,
    'reviews' => 16,
    'features' => [
        'Height: 25cm',
        'Material: PVC',
        'Manufacturer: Anime Collectibles',
        'Limited Edition',
        'Detailed Sculpting'
    ],
    'images' => [
        'assets/images/Kyo2.jpg',
        'assets/images/Kyo2.jpg',
        'assets/images/Kyo2.jpg',
        'assets/images/Kyo2.jpg',
    ],
    'related' => [
        ['id' => 2, 'name' => 'Related Product 1', 'price' => 39.99, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Figures'],
        ['id' => 3, 'name' => 'Related Product 2', 'price' => 24.99, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Accessories'],
        ['id' => 4, 'name' => 'Related Product 3', 'price' => 29.99, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Clothing'],
        ['id' => 5, 'name' => 'Related Product 4', 'price' => 19.99, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Manga'],
    ]
];

?>

<section class="container py-10">
    <div class="flex flex-col space-y-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <div class="bg-white p-2 rounded-lg shadow-md mb-4">
                    <img id="mainImage" src="<?= $product['images'][0] ?>"
                        alt="<?= htmlspecialchars($product['name']) ?>"
                        class="w-full h-auto object-cover rounded-md aspect-square">
                </div>
                <div class="grid grid-cols-4 gap-2">
                    <?php foreach ($product['images'] as $index => $image): ?>
                        <div class="bg-white p-1 rounded-md shadow-sm cursor-pointer hover:shadow-md transition"
                            onclick="changeMainImage('<?= $image ?>', <?= $index ?>)">
                            <img src="<?= $image ?>" alt="Thumbnail" class="w-full h-auto object-cover rounded-md aspect-square 
                                   <?= $index === 0 ? 'ring-2 ring-red-600' : '' ?>" id="thumb-<?= $index ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="space-y-6">
                <div>
                    <span class="text-sm text-red-600 font-medium"><?= htmlspecialchars($product['category']) ?></span>
                    <h1 class="text-2xl md:text-3xl font-bold"><?= htmlspecialchars($product['name']) ?></h1>

                    <div class="flex items-center mt-2">
                        <div class="flex items-center">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <?php if ($i <= floor($product['rating'])): ?>
                                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                        </path>
                                    </svg>
                                <?php elseif ($i === ceil($product['rating']) && $product['rating'] != floor($product['rating'])): ?>
                                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                        </path>
                                    </svg>
                                <?php else: ?>
                                    <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                        </path>
                                    </svg>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                        <p class="ml-2 text-sm text-gray-600"><?= $product['rating'] ?> (<?= $product['reviews'] ?>
                            reviews)</p>
                    </div>
                </div>

                <div>
                    <?php if ($product['stock'] > 0): ?>
                        <p class="text-sm text-green-600 mt-1">
                            <svg class="inline-block w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            In Stock (<?= $product['stock'] ?> available)
                        </p>
                    <?php else: ?>
                        <p class="text-sm text-red-600 mt-1">
                            <svg class="inline-block w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Out of Stock
                        </p>
                    <?php endif; ?>
                </div>

                <div>
                    <p class="text-gray-700"><?= htmlspecialchars($product['description']) ?></p>
                </div>

                <div class="space-y-1">
                    <h3 class="font-semibold">Features:</h3>
                    <ul class="list-disc pl-5 text-gray-700 space-y-1">
                        <?php foreach ($product['features'] as $feature): ?>
                            <li><?= htmlspecialchars($feature) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div class="pt-4 space-y-4">
                    <div class="flex items-center space-x-4">
                        <button onclick="addToCart(<?= $product['id'] ?>)"
                            class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            Add to Cart
                        </button>

                        <button onclick="toggleFavorite(<?= $product['id'] ?>)"
                            class="bg-gray-100 p-2 rounded-lg hover:bg-gray-200 transition">
                            <svg class="w-6 h-6 text-gray-600" id="favorite-icon" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                </path>
                            </svg>
                        </button>
                    </div>

                    <div class="flex items-center space-x-4 text-sm">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-500 mr-1" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Fast Shipping</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-500 mr-1" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                </path>
                            </svg>
                            <span>Authentic Product</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-12">
            <h2 class="text-xl md:text-2xl font-bold mb-6">You May Also Like</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php foreach ($product['related'] as $relatedProduct): ?>
                    <article class="group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col">
                        <div class="overflow-hidden">
                            <img src="<?= $relatedProduct['img'] ?>" alt="<?= htmlspecialchars($relatedProduct['name']) ?>"
                                class="object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105">
                        </div>
                        <div class="p-4 flex flex-col justify-between flex-1">
                            <div>
                                <p class="text-xs text-red-500 uppercase font-semibold">
                                    <?= htmlspecialchars($relatedProduct['category']) ?></p>
                                <p class="text-sm font-bold text-gray-800 truncate">
                                    <?= htmlspecialchars($relatedProduct['name']) ?></p>
                                <p class="text-sm text-gray-600">
                                    <?= number_format($relatedProduct['price'], 2) ?> USD
                                </p>
                            </div>
                            <a href="/product-details.php?id=<?= $relatedProduct['id'] ?>"
                                class="mt-4 inline-block text-center bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200">
                                View Details
                            </a>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<script>
    let quantity = 1;
    let isFavorite = false;

    function updateQuantity(action) {
        if (action === 'increase') {
            quantity++;
        } else if (action === 'decrease' && quantity > 1) {
            quantity--;
        }
        document.getElementById('quantity').value = quantity;
    }

    function addToCart(productId) {
        console.log(`Add to cart: Product ID ${productId}, Quantity: ${quantity}`);
        alert('Product added to cart!');
    }

    function toggleFavorite(productId) {
        isFavorite = !isFavorite;
        const icon = document.getElementById('favorite-icon');

        if (isFavorite) {
            icon.classList.remove('text-gray-600');
            icon.classList.add('text-red-600');
            icon.setAttribute('fill', 'currentColor');
        } else {
            icon.classList.remove('text-red-600');
            icon.classList.add('text-gray-600');
            icon.setAttribute('fill', 'none');
        }

        console.log(`Toggle favorite: Product ID ${productId}, Status: ${isFavorite}`);
    }

    function changeMainImage(src, index) {
        document.getElementById('mainImage').src = src;

        const thumbs = document.querySelectorAll('[id^="thumb-"]');
        thumbs.forEach(thumb => thumb.classList.remove('ring-2', 'ring-red-600'));
        document.getElementById(`thumb-${index}`).classList.add('ring-2', 'ring-red-600');
    }
</script>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>