<?php
$title = "Products";
include_once(__DIR__ . '/../includes/store/header.php');
$Products = [
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
    ['id' => 1, 'img' => 'img/Daki.png', 'category' => 'Figures', 'name' => 'Product A', 'price' => 49.99],
];
?>
<div class="container flex flex-col md:flex-row w-full gap-6 py-10 mt-8">
    <aside class="hidden lg:block w-64 sticky top-21 h-fit border border-gray-200 shadow rounded-lg p-6 bg-white">
        <h2 class="text-lg font-bold mb-4">Categories</h2>
        <div class="space-y-2">
            <label class="flex items-center gap-2">
                <input type="checkbox" class="accent-red-600"> Figures & Statues
            </label>
            <label class="flex items-center gap-2">
                <input type="checkbox" class="accent-red-600"> Clothing & Apparel
            </label>
            <label class="flex items-center gap-2">
                <input type="checkbox" class="accent-red-600"> Accessories
            </label>
            <label class="flex items-center gap-2">
                <input type="checkbox" class="accent-red-600"> Manga & Books
            </label>
            <label class="flex items-center gap-2">
                <input type="checkbox" class="accent-red-600"> Plushies & Toys
            </label>
        </div>
        <hr class="my-6 border-gray-300">
        <h2 class="text-lg font-bold">Price Range</h2>
        <div class="flex gap-2 mt-2">
            <input type="text" class="w-1/2 rounded border p-1" placeholder="Min">.
            <input type="text" class="w-1/2 rounded border p-1" placeholder="Max">
        </div>
        <hr class="my-6 border-gray-300">
        <h2 class="text-lg font-bold">Availability</h2>
        <label class="flex items-center gap-2 mt-2">
            <input type="checkbox" class="accent-red-600"> On Sale
        </label>
    </aside>
    <section class="flex-1">
        <div class="flex flex-wrap items-start justify-between gap-2">
            <h1 class="text-[25px] font-bold">Products</h1>

            <div class="flex flex-wrap gap-2 sm:flex-nowrap w-full sm:w-auto">
                <button id="openFilter"
                    class="bg-red-600 lg:hidden text-white px-4 py-2 rounded-md font-semibold hover:bg-red-500 w-full sm:w-auto">
                    Add Filter
                </button>
                <select id="SortBy" name="SortBy"
                    class="px-1 py-2 border border-gray-300 rounded-lg focus:outline-none bg-white text-gray-700 w-full sm:w-auto">
                    <option value="Featured">Featured</option>
                    <option value="Name (A-Z)">Name (A-Z)</option>
                    <option value="Price - Low to High">Price - Low to High</option>
                    <option value="Price - High to Low">Price - High to Low</option>
                </select>
            </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mt-5">
            <?php foreach ($Products as $Product): ?>
                <article
                    class="group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto">
                    <div class="overflow-hidden">
                        <img src="<?= "images/" ?>website/Kyo2.jpg" alt="Product Image"
                            class="object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105">
                    </div>
                    <div class="p-4 flex flex-col justify-between flex-1">
                        <div>
                            <p class="text-xs text-red-500 uppercase font-semibold"><?= $Product['category'] ?></p>
                            <p class="text-sm font-bold text-gray-800 truncate"><?= $Product['name'] ?></p>
                            <p class="text-sm text-gray-600">
                                <?= number_format($Product['price'], 2) ?> USD
                            </p>
                        </div>
                        <a href="#"
                            class="mt-4 inline-block text-center bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200">
                            Add to Cart
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </section>
</div>
<div id="filterModal" class="flex fixed inset-0 z-50 bg-black/50 hidden items-center justify-center lg:hidden">
    <div class="bg-white w-11/12 max-w-md p-6 rounded-lg relative">
        <button id="closeFilter"
            class="absolute top-3 right-4 text-gray-600 hover:text-red-500 text-xl">&times;</button>
        <h2 class="text-xl font-bold mb-4">Filters</h2>
        <div class="space-y-4">
            <div>
                <h3 class="font-semibold mb-1">Categories</h3>
                <div class="space-y-2">
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="accent-red-600"> Figures & Statues
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="accent-red-600"> Clothing & Apparel
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="accent-red-600"> Accessories
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="accent-red-600"> Manga & Books
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="accent-red-600"> Plushies & Toys
                    </label>
                </div>
            </div>
            <div>
                <h3 class="font-semibold mb-1">Price Range</h3>
                <div class="flex gap-2">
                    <input type="text" class="w-1/2 rounded border p-1" placeholder="Min">.
                    <input type="text" class="w-1/2 rounded border p-1" placeholder="Max">
                </div>
            </div>
            <div>
                <h3 class="font-semibold mb-1">Availability</h3>
                <label class="flex items-center gap-2">
                    <input type="checkbox" class="accent-red-600"> On Sale
                </label>
            </div>
        </div>
        <button class="mt-6 w-full bg-red-600 text-white py-2 rounded-md font-semibold hover:bg-red-500">
            Apply Filter
        </button>
    </div>
</div>
<script>
    const openBtn = document.getElementById('openFilter');
    const closeBtn = document.getElementById('closeFilter');
    const modal = document.getElementById('filterModal');
    openBtn.addEventListener('click', () => modal.classList.remove('hidden'));
    closeBtn.addEventListener('click', () => modal.classList.add('hidden'));
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.classList.add('hidden');
    });
</script>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>