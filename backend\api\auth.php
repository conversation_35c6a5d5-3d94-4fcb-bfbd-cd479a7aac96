<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// Get action from URL or input
$action = $_GET['action'] ?? $input['action'] ?? '';

switch ($method) {
    case 'POST':
        if ($action === 'login') {
            login();
        } elseif ($action === 'register') {
            register();
        } elseif ($action === 'logout') {
            logout();
        } elseif ($action === 'transfer-cart') {
            transferGuestCart();
        } else {
            sendResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    case 'GET':
        if ($action === 'me') {
            getMe();
        } else {
            sendResponse(['error' => 'Invalid endpoint'], 404);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function login() {
    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['email', 'password']);

    global $mysqli;

    $email = escapeString($data['email']);
    $query = "SELECT * FROM Users WHERE email = '$email'";
    $user = getSingleRow($query);

    if (!$user || !password_verify($data['password'], $user['password'])) {
        sendResponse(['error' => 'Invalid credentials'], 401);
    }

    session_start();
    $_SESSION['user'] = [
        'id' => $user['id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'role' => $user['role']
    ];

    sendResponse([
        'message' => 'Login successful',
        'user' => $_SESSION['user']
    ]);
}

function register() {
    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['name', 'email', 'password']);

    global $mysqli;

    // Check if email already exists
    $email = escapeString($data['email']);
    $checkQuery = "SELECT id FROM Users WHERE email = '$email'";
    $existing = getSingleRow($checkQuery);

    if ($existing) {
        sendResponse(['error' => 'Email already exists'], 400);
    }

    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    $name = escapeString($data['name']);

    $insertQuery = "INSERT INTO Users (name, email, password) VALUES ('$name', '$email', '$hashedPassword')";
    executeQuery($insertQuery);

    $userId = mysqli_insert_id($mysqli);

    session_start();
    $_SESSION['user'] = [
        'id' => $userId,
        'name' => $data['name'],
        'email' => $data['email'],
        'role' => 'user'
    ];

    sendResponse([
        'message' => 'Registration successful',
        'user' => $_SESSION['user']
    ], 201);
}

function logout() {
    session_start();
    session_destroy();
    sendResponse(['message' => 'Logout successful']);
}

function getMe() {
    session_start();
    if (!isset($_SESSION['user'])) {
        sendResponse(['error' => 'Not authenticated'], 401);
    }

    sendResponse(['user' => $_SESSION['user']]);
}

function transferGuestCart() {
    $user = getCurrentUser();
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['cartItems']) || !is_array($data['cartItems'])) {
        sendResponse(['message' => 'No cart items to transfer']);
    }

    global $mysqli;

    // Get or create cart order for user
    $userId = $user['id'];
    $cartQuery = "SELECT id FROM Orders WHERE user_id = $userId AND status = 'cart'";
    $cartOrder = getSingleRow($cartQuery);

    if (!$cartOrder) {
        $createCartQuery = "INSERT INTO Orders (user_id, status) VALUES ($userId, 'cart')";
        executeQuery($createCartQuery);
        $orderId = mysqli_insert_id($mysqli);
    } else {
        $orderId = $cartOrder['id'];
    }

    // Transfer each item from localStorage to database
    foreach ($data['cartItems'] as $item) {
        $productId = (int)$item['product_id'];
        $quantity = (int)$item['quantity'];
        $price = (float)$item['price'];

        // Check if item already exists in cart
        $existingQuery = "SELECT * FROM OrderItems WHERE order_id = $orderId AND product_id = $productId";
        $existing = getSingleRow($existingQuery);

        if ($existing) {
            // Update quantity
            $newQuantity = $existing['quantity'] + $quantity;
            $updateQuery = "UPDATE OrderItems SET quantity = $newQuantity WHERE id = {$existing['id']}";
            executeQuery($updateQuery);
        } else {
            // Add new item
            $insertQuery = "INSERT INTO OrderItems (order_id, product_id, quantity, price) VALUES ($orderId, $productId, $quantity, $price)";
            executeQuery($insertQuery);
        }
    }

    sendResponse(['message' => 'Cart transferred successfully']);
}
