{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\admin\\\\Users.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Users = ({\n  user\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setUsers([{\n        id: 1,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        role: 'user',\n        created_at: '2024-01-15',\n        orders: 5\n      }, {\n        id: 2,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        role: 'user',\n        created_at: '2024-01-14',\n        orders: 3\n      }, {\n        id: 3,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        role: 'user',\n        created_at: '2024-01-13',\n        orders: 8\n      }, {\n        id: 4,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        role: 'admin',\n        created_at: '2024-01-12',\n        orders: 0\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const getRoleColor = role => {\n    return role === 'admin' ? 'text-red-600 bg-red-100' : 'text-blue-600 bg-blue-100';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      user: user,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search users...\",\n            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"All Roles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"User\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Joined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: users.map(userData => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: userData.name.split(' ').map(n => n[0]).join('')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 81,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: userData.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: userData.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 87,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(userData.role)}`,\n                  children: userData.role.charAt(0).toUpperCase() + userData.role.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: [userData.orders, \" orders\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: userData.created_at\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                  children: \"Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this)]\n            }, userData.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-blue-100 text-blue-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: users.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-green-100 text-green-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Active Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: users.filter(u => u.orders > 0).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-red-100 text-red-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Admins\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: users.filter(u => u.role === 'admin').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Users, \"+quVjBMM9THpHvnUcBaphXhhZmo=\");\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminLayout", "jsxDEV", "_jsxDEV", "Users", "user", "_s", "users", "setUsers", "loading", "setLoading", "setTimeout", "id", "name", "email", "role", "created_at", "orders", "getRoleColor", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "map", "userData", "split", "n", "join", "char<PERSON>t", "toUpperCase", "slice", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "filter", "u", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/admin/Users.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\n\nconst Users = ({ user }) => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setUsers([\n        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'user', created_at: '2024-01-15', orders: 5 },\n        { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'user', created_at: '2024-01-14', orders: 3 },\n        { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'user', created_at: '2024-01-13', orders: 8 },\n        { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'admin', created_at: '2024-01-12', orders: 0 },\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getRoleColor = (role) => {\n    return role === 'admin' ? 'text-red-600 bg-red-100' : 'text-blue-600 bg-blue-100';\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout user={user}>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout user={user}>\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Users</h1>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              placeholder=\"Search users...\"\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n            />\n            <select className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\">\n              <option>All Roles</option>\n              <option>Admin</option>\n              <option>User</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  User\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Role\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Orders\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Joined\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {users.map((userData) => (\n                <tr key={userData.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-gray-700\">\n                          {userData.name.split(' ').map(n => n[0]).join('')}\n                        </span>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{userData.name}</div>\n                        <div className=\"text-sm text-gray-500\">{userData.email}</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(userData.role)}`}>\n                      {userData.role.charAt(0).toUpperCase() + userData.role.slice(1)}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {userData.orders} orders\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {userData.created_at}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 mr-3\">Edit</button>\n                    <button className=\"text-red-600 hover:text-red-900\">Delete</button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-blue-100 text-blue-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{users.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-green-100 text-green-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Active Users</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{users.filter(u => u.orders > 0).length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-red-100 text-red-600\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Admins</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{users.filter(u => u.role === 'admin').length}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Users;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACAW,UAAU,CAAC,MAAM;MACfH,QAAQ,CAAC,CACP;QAAEI,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,IAAI,EAAE,MAAM;QAAEC,UAAU,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAE,CAAC,EACzG;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,IAAI,EAAE,MAAM;QAAEC,UAAU,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAE,CAAC,EAC3G;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,IAAI,EAAE,MAAM;QAAEC,UAAU,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAE,CAAC,EAC7G;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,mBAAmB;QAAEC,IAAI,EAAE,OAAO;QAAEC,UAAU,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAE,CAAC,CAChH,CAAC;MACFP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAIH,IAAI,IAAK;IAC7B,OAAOA,IAAI,KAAK,OAAO,GAAG,yBAAyB,GAAG,2BAA2B;EACnF,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBACEN,OAAA,CAACF,WAAW;MAACI,IAAI,EAAEA,IAAK;MAAAc,QAAA,eACtBhB,OAAA;QAAKiB,SAAS,EAAC,gDAAgD;QAAAD,QAAA,eAC7DhB,OAAA;UAAKiB,SAAS,EAAC;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;EAEA,oBACErB,OAAA,CAACF,WAAW;IAACI,IAAI,EAAEA,IAAK;IAAAc,QAAA,eACtBhB,OAAA;MAAKiB,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBhB,OAAA;QAAKiB,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDhB,OAAA;UAAIiB,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DrB,OAAA;UAAKiB,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BhB,OAAA;YACEsB,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,iBAAiB;YAC7BN,SAAS,EAAC;UAAgG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eACFrB,OAAA;YAAQiB,SAAS,EAAC,gGAAgG;YAAAD,QAAA,gBAChHhB,OAAA;cAAAgB,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1BrB,OAAA;cAAAgB,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtBrB,OAAA;cAAAgB,QAAA,EAAQ;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrB,OAAA;QAAKiB,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5DhB,OAAA;UAAOiB,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBACpDhB,OAAA;YAAOiB,SAAS,EAAC,YAAY;YAAAD,QAAA,eAC3BhB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAIiB,SAAS,EAAC,gFAAgF;gBAAAD,QAAA,EAAC;cAE/F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,gFAAgF;gBAAAD,QAAA,EAAC;cAE/F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,gFAAgF;gBAAAD,QAAA,EAAC;cAE/F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,gFAAgF;gBAAAD,QAAA,EAAC;cAE/F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,gFAAgF;gBAAAD,QAAA,EAAC;cAE/F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrB,OAAA;YAAOiB,SAAS,EAAC,mCAAmC;YAAAD,QAAA,EACjDZ,KAAK,CAACoB,GAAG,CAAEC,QAAQ,iBAClBzB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAIiB,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eACzChB,OAAA;kBAAKiB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChChB,OAAA;oBAAKiB,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,eAClFhB,OAAA;sBAAMiB,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAChDS,QAAQ,CAACf,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACF,GAAG,CAACG,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNrB,OAAA;oBAAKiB,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBhB,OAAA;sBAAKiB,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAAES,QAAQ,CAACf;oBAAI;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxErB,OAAA;sBAAKiB,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAES,QAAQ,CAACd;oBAAK;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eACzChB,OAAA;kBAAMiB,SAAS,EAAE,8CAA8CF,YAAY,CAACU,QAAQ,CAACb,IAAI,CAAC,EAAG;kBAAAI,QAAA,EAC1FS,QAAQ,CAACb,IAAI,CAACiB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGL,QAAQ,CAACb,IAAI,CAACmB,KAAK,CAAC,CAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,mDAAmD;gBAAAD,QAAA,GAC9DS,QAAQ,CAACX,MAAM,EAAC,SACnB;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,mDAAmD;gBAAAD,QAAA,EAC9DS,QAAQ,CAACZ;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACLrB,OAAA;gBAAIiB,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,gBAC7DhB,OAAA;kBAAQiB,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxErB,OAAA;kBAAQiB,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA,GA5BEI,QAAQ,CAAChB,EAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BhB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrB,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpDhB,OAAA;UAAKiB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDhB,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChChB,OAAA;cAAKiB,SAAS,EAAC,4CAA4C;cAAAD,QAAA,eACzDhB,OAAA;gBAAKiB,SAAS,EAAC,SAAS;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC5EhB,OAAA;kBAAMmC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA+G;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrB,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhB,OAAA;gBAAGiB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChErB,OAAA;gBAAGiB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEZ,KAAK,CAACmC;cAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrB,OAAA;UAAKiB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDhB,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChChB,OAAA;cAAKiB,SAAS,EAAC,8CAA8C;cAAAD,QAAA,eAC3DhB,OAAA;gBAAKiB,SAAS,EAAC,SAAS;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC5EhB,OAAA;kBAAMmC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA+C;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrB,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhB,OAAA;gBAAGiB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjErB,OAAA;gBAAGiB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEZ,KAAK,CAACoC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3B,MAAM,GAAG,CAAC,CAAC,CAACyB;cAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrB,OAAA;UAAKiB,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChDhB,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChChB,OAAA;cAAKiB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,eACvDhB,OAAA;gBAAKiB,SAAS,EAAC,SAAS;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC5EhB,OAAA;kBAAMmC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAgM;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrB,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhB,OAAA;gBAAGiB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3DrB,OAAA;gBAAGiB,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEZ,KAAK,CAACoC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,IAAI,KAAK,OAAO,CAAC,CAAC2B;cAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAClB,EAAA,CA3JIF,KAAK;AAAAyC,EAAA,GAALzC,KAAK;AA6JX,eAAeA,KAAK;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}