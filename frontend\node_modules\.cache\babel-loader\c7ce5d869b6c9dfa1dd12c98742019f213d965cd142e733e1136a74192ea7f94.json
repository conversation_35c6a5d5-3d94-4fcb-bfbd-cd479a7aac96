{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\admin\\\\Settings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = ({\n  user\n}) => {\n  _s();\n  const [settings, setSettings] = useState({\n    store_name: '',\n    store_description: '',\n    store_email: '',\n    store_phone: '',\n    store_address: '',\n    facebook_url: '',\n    instagram_url: '',\n    tiktok_url: '',\n    whatsapp_url: '',\n    telegram_url: '',\n    x_url: '',\n    shipping_fee: '',\n    free_shipping_threshold: '',\n    tax_rate: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setSettings({\n        store_name: 'KYOPAL',\n        store_description: 'Your ultimate destination for premium anime merchandise and collectibles.',\n        store_email: '<EMAIL>',\n        store_phone: '+970 XXX XXXX',\n        store_address: 'Palestine',\n        facebook_url: 'https://facebook.com/kyopal',\n        instagram_url: 'https://instagram.com/kyopal',\n        tiktok_url: 'https://tiktok.com/@kyopal',\n        whatsapp_url: 'https://wa.me/970XXXXXXX',\n        telegram_url: 'https://t.me/kyopal',\n        x_url: 'https://x.com/kyopal',\n        shipping_fee: '5.00',\n        free_shipping_threshold: '50.00',\n        tax_rate: '10.00'\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const handleChange = e => {\n    setSettings({\n      ...settings,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSaving(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      setSaving(false);\n      alert('Settings saved successfully!');\n    }, 2000);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      user: user,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Store Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Store Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Store Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"store_name\",\n                value: settings.store_name,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Store Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"store_email\",\n                value: settings.store_email,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Store Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"store_phone\",\n                value: settings.store_phone,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Store Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"store_address\",\n                value: settings.store_address,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Store Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"store_description\",\n                rows: \"3\",\n                value: settings.store_description,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Social Media Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Facebook URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"facebook_url\",\n                value: settings.facebook_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Instagram URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"instagram_url\",\n                value: settings.instagram_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"TikTok URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"tiktok_url\",\n                value: settings.tiktok_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"WhatsApp URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"whatsapp_url\",\n                value: settings.whatsapp_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Telegram URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"telegram_url\",\n                value: settings.telegram_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"X (Twitter) URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                name: \"x_url\",\n                value: settings.x_url,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Shipping & Tax Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Shipping Fee ($)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                name: \"shipping_fee\",\n                value: settings.shipping_fee,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Free Shipping Threshold ($)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                name: \"free_shipping_threshold\",\n                value: settings.free_shipping_threshold,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tax Rate (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                name: \"tax_rate\",\n                value: settings.tax_rate,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: saving,\n            className: \"bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: saving ? 'Saving...' : 'Save Settings'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"6MmPJ7jlt3NpM6KjvFazS+TIFO8=\");\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminLayout", "jsxDEV", "_jsxDEV", "Settings", "user", "_s", "settings", "setSettings", "store_name", "store_description", "store_email", "store_phone", "store_address", "facebook_url", "instagram_url", "tiktok_url", "whatsapp_url", "telegram_url", "x_url", "shipping_fee", "free_shipping_threshold", "tax_rate", "loading", "setLoading", "saving", "setSaving", "setTimeout", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "alert", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "rows", "step", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/admin/Settings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminLayout from '../../components/AdminLayout';\n\nconst Settings = ({ user }) => {\n  const [settings, setSettings] = useState({\n    store_name: '',\n    store_description: '',\n    store_email: '',\n    store_phone: '',\n    store_address: '',\n    facebook_url: '',\n    instagram_url: '',\n    tiktok_url: '',\n    whatsapp_url: '',\n    telegram_url: '',\n    x_url: '',\n    shipping_fee: '',\n    free_shipping_threshold: '',\n    tax_rate: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setSettings({\n        store_name: 'KYOPAL',\n        store_description: 'Your ultimate destination for premium anime merchandise and collectibles.',\n        store_email: '<EMAIL>',\n        store_phone: '+970 XXX XXXX',\n        store_address: 'Palestine',\n        facebook_url: 'https://facebook.com/kyopal',\n        instagram_url: 'https://instagram.com/kyopal',\n        tiktok_url: 'https://tiktok.com/@kyopal',\n        whatsapp_url: 'https://wa.me/970XXXXXXX',\n        telegram_url: 'https://t.me/kyopal',\n        x_url: 'https://x.com/kyopal',\n        shipping_fee: '5.00',\n        free_shipping_threshold: '50.00',\n        tax_rate: '10.00'\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const handleChange = (e) => {\n    setSettings({\n      ...settings,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      setSaving(false);\n      alert('Settings saved successfully!');\n    }, 2000);\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout user={user}>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout user={user}>\n      <div className=\"space-y-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Store Settings</h1>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Store Information */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Store Information</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Store Name</label>\n                <input\n                  type=\"text\"\n                  name=\"store_name\"\n                  value={settings.store_name}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Store Email</label>\n                <input\n                  type=\"email\"\n                  name=\"store_email\"\n                  value={settings.store_email}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Store Phone</label>\n                <input\n                  type=\"text\"\n                  name=\"store_phone\"\n                  value={settings.store_phone}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Store Address</label>\n                <input\n                  type=\"text\"\n                  name=\"store_address\"\n                  value={settings.store_address}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Store Description</label>\n                <textarea\n                  name=\"store_description\"\n                  rows=\"3\"\n                  value={settings.store_description}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                ></textarea>\n              </div>\n            </div>\n          </div>\n\n          {/* Social Media */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Social Media Links</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Facebook URL</label>\n                <input\n                  type=\"url\"\n                  name=\"facebook_url\"\n                  value={settings.facebook_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Instagram URL</label>\n                <input\n                  type=\"url\"\n                  name=\"instagram_url\"\n                  value={settings.instagram_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">TikTok URL</label>\n                <input\n                  type=\"url\"\n                  name=\"tiktok_url\"\n                  value={settings.tiktok_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">WhatsApp URL</label>\n                <input\n                  type=\"url\"\n                  name=\"whatsapp_url\"\n                  value={settings.whatsapp_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Telegram URL</label>\n                <input\n                  type=\"url\"\n                  name=\"telegram_url\"\n                  value={settings.telegram_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">X (Twitter) URL</label>\n                <input\n                  type=\"url\"\n                  name=\"x_url\"\n                  value={settings.x_url}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Shipping & Tax */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Shipping & Tax Settings</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Shipping Fee ($)</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  name=\"shipping_fee\"\n                  value={settings.shipping_fee}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Free Shipping Threshold ($)</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  name=\"free_shipping_threshold\"\n                  value={settings.free_shipping_threshold}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tax Rate (%)</label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  name=\"tax_rate\"\n                  value={settings.tax_rate}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={saving}\n              className=\"bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {saving ? 'Saving...' : 'Save Settings'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE,EAAE;IAChBC,uBAAuB,EAAE,EAAE;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd;IACA2B,UAAU,CAAC,MAAM;MACfnB,WAAW,CAAC;QACVC,UAAU,EAAE,QAAQ;QACpBC,iBAAiB,EAAE,2EAA2E;QAC9FC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,aAAa,EAAE,WAAW;QAC1BC,YAAY,EAAE,6BAA6B;QAC3CC,aAAa,EAAE,8BAA8B;QAC7CC,UAAU,EAAE,4BAA4B;QACxCC,YAAY,EAAE,0BAA0B;QACxCC,YAAY,EAAE,qBAAqB;QACnCC,KAAK,EAAE,sBAAsB;QAC7BC,YAAY,EAAE,MAAM;QACpBC,uBAAuB,EAAE,OAAO;QAChCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1BrB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACsB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,SAAS,CAAC,IAAI,CAAC;;IAEf;IACAC,UAAU,CAAC,MAAM;MACfD,SAAS,CAAC,KAAK,CAAC;MAChBS,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAIZ,OAAO,EAAE;IACX,oBACEpB,OAAA,CAACF,WAAW;MAACI,IAAI,EAAEA,IAAK;MAAA+B,QAAA,eACtBjC,OAAA;QAAKkC,SAAS,EAAC,gDAAgD;QAAAD,QAAA,eAC7DjC,OAAA;UAAKkC,SAAS,EAAC;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;EAEA,oBACEtC,OAAA,CAACF,WAAW;IAACI,IAAI,EAAEA,IAAK;IAAA+B,QAAA,eACtBjC,OAAA;MAAKkC,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBjC,OAAA;QAAIkC,SAAS,EAAC,kCAAkC;QAAAD,QAAA,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEpEtC,OAAA;QAAMuC,QAAQ,EAAET,YAAa;QAACI,SAAS,EAAC,WAAW;QAAAD,QAAA,gBAEjDjC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDjC,OAAA;YAAIkC,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EtC,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDjC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFtC,OAAA;gBACEwC,IAAI,EAAC,MAAM;gBACXZ,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEzB,QAAQ,CAACE,UAAW;gBAC3BmC,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFtC,OAAA;gBACEwC,IAAI,EAAC,OAAO;gBACZZ,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAEzB,QAAQ,CAACI,WAAY;gBAC5BiC,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFtC,OAAA;gBACEwC,IAAI,EAAC,MAAM;gBACXZ,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAEzB,QAAQ,CAACK,WAAY;gBAC5BgC,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFtC,OAAA;gBACEwC,IAAI,EAAC,MAAM;gBACXZ,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAEzB,QAAQ,CAACM,aAAc;gBAC9B+B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAKkC,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5BjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFtC,OAAA;gBACE4B,IAAI,EAAC,mBAAmB;gBACxBc,IAAI,EAAC,GAAG;gBACRb,KAAK,EAAEzB,QAAQ,CAACG,iBAAkB;gBAClCkC,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDjC,OAAA;YAAIkC,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFtC,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDjC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,cAAc;gBACnBC,KAAK,EAAEzB,QAAQ,CAACO,YAAa;gBAC7B8B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAEzB,QAAQ,CAACQ,aAAc;gBAC9B6B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEzB,QAAQ,CAACS,UAAW;gBAC3B4B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,cAAc;gBACnBC,KAAK,EAAEzB,QAAQ,CAACU,YAAa;gBAC7B2B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,cAAc;gBACnBC,KAAK,EAAEzB,QAAQ,CAACW,YAAa;gBAC7B0B,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtC,OAAA;gBACEwC,IAAI,EAAC,KAAK;gBACVZ,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEzB,QAAQ,CAACY,KAAM;gBACtByB,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDjC,OAAA;YAAIkC,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFtC,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDjC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxFtC,OAAA;gBACEwC,IAAI,EAAC,QAAQ;gBACbG,IAAI,EAAC,MAAM;gBACXf,IAAI,EAAC,cAAc;gBACnBC,KAAK,EAAEzB,QAAQ,CAACa,YAAa;gBAC7BwB,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnGtC,OAAA;gBACEwC,IAAI,EAAC,QAAQ;gBACbG,IAAI,EAAC,MAAM;gBACXf,IAAI,EAAC,yBAAyB;gBAC9BC,KAAK,EAAEzB,QAAQ,CAACc,uBAAwB;gBACxCuB,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAAiC,QAAA,gBACEjC,OAAA;gBAAOkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpFtC,OAAA;gBACEwC,IAAI,EAAC,QAAQ;gBACbG,IAAI,EAAC,MAAM;gBACXf,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEzB,QAAQ,CAACe,QAAS;gBACzBsB,QAAQ,EAAEhB,YAAa;gBACvBS,SAAS,EAAC;cAAuG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtC,OAAA;UAAKkC,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC/BjC,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEtB,MAAO;YACjBY,SAAS,EAAC,6GAA6G;YAAAD,QAAA,EAEtHX,MAAM,GAAG,WAAW,GAAG;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAACnC,EAAA,CA9PIF,QAAQ;AAAA4C,EAAA,GAAR5C,QAAQ;AAgQd,eAAeA,QAAQ;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}