{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\KyoPalWebsite - Copy (2)\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [products, setProducts] = useState([]);\n\n  // Sample data - replace with API calls later\n  useEffect(() => {\n    // Sample categories\n    setCategories([{\n      id: 1,\n      img: '/images/categories/1.png',\n      name: 'Figures',\n      count: '25'\n    }, {\n      id: 2,\n      img: '/images/categories/2.png',\n      name: 'Clothing',\n      count: '18'\n    }, {\n      id: 3,\n      img: '/images/categories/3.png',\n      name: 'Accessories',\n      count: '32'\n    }, {\n      id: 4,\n      img: '/images/categories/4.png',\n      name: 'Manga',\n      count: '45'\n    }, {\n      id: 5,\n      img: '/images/categories/5.png',\n      name: 'Posters',\n      count: '12'\n    }, {\n      id: 6,\n      img: '/images/categories/6.png',\n      name: 'Keychains',\n      count: '28'\n    }, {\n      id: 7,\n      img: '/images/categories/7.png',\n      name: 'Plushies',\n      count: '15'\n    }, {\n      id: 8,\n      img: '/images/categories/8.png',\n      name: 'Collectibles',\n      count: '22'\n    }]);\n\n    // Sample products\n    setProducts([{\n      id: 1,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Figures',\n      name: 'Anime Figure Collection',\n      price: 49.99\n    }, {\n      id: 2,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Figures',\n      name: 'Premium Statue',\n      price: 89.99\n    }, {\n      id: 3,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Clothing',\n      name: 'Anime T-Shirt',\n      price: 24.99\n    }, {\n      id: 4,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Accessories',\n      name: 'Character Keychain',\n      price: 12.99\n    }, {\n      id: 5,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Manga',\n      name: 'Popular Manga Series',\n      price: 15.99\n    }, {\n      id: 6,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Plushies',\n      name: 'Cute Character Plush',\n      price: 29.99\n    }, {\n      id: 7,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Posters',\n      name: 'Anime Wall Poster',\n      price: 19.99\n    }, {\n      id: 8,\n      img: '/images/website/Kyo2.jpg',\n      category: 'Collectibles',\n      name: 'Limited Edition Item',\n      price: 99.99\n    }]);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"font-merienda min-h-screen flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/website/background.jpg\",\n        className: \"absolute inset-0 w-full h-full object-cover opacity-5 z-0\",\n        alt: \"background\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container relative z-10 text-center md:text-left space-y-6 md:space-y-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-red-600 text-md md:text-2xl font-bold\",\n          children: \"FIRST ANIME STORE IN PALESTINE!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-black font-extrabold text-3xl md:text-4xl\",\n          children: [\"Discover World of \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-600\",\n            children: \"Anime\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 31\n          }, this), \" Collectibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-black/70 text-lg md:w-2/3\",\n          children: \"Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center md:justify-start gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md\",\n            children: \"Shop New\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"container w-full text-center pt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-extrabold\",\n        children: \"Shop by Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-700 mt-4\",\n        children: \"Explore our wide range of anime merchandise categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"article\", {\n          className: \"bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 cursor-pointer hover:scale-105 transition-transform\",\n          style: {\n            backgroundImage: `url('${category.img}')`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative top-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-extrabold text-white\",\n              children: category.name || 'Category Name'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-white mt-2\",\n              children: [category.count || '0', \" products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"container pt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm\",\n          children: \"Trending Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-extrabold text-gray-800\",\n          children: \"Featured Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(\"article\", {\n          className: \"group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.img,\n              alt: \"Product Image\",\n              className: \"object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 flex flex-col justify-between flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-500 uppercase font-semibold\",\n                children: product.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-bold text-gray-800 truncate\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"$\", product.price.toFixed(2), \" USD\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"mt-4 inline-block text-center bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200\",\n              children: \"Add to Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mt-12\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          className: \"flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-300 hover:bg-red-50 transition-transform duration-300 ease-in-out\",\n          children: [\"View All Products\", /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 ml-2\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              stroke: \"#000000\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"m19 12-6-6m6 6-6 6m6-6H5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12 bg-red-600 text-white mt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\",\n              children: \"Special Offers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"max-w-[600px] md:text-xl\",\n              children: \"Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 min-[400px]:flex-row\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?sale=true\",\n                className: \"inline-block\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]\",\n                  children: \"Shop Sale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"20% OFF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"On selected figures\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"FREE SHIPPING\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"On orders over $50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"GUARANTEE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"30-day money back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"REWARDS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Earn points with every purchase\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"yk8rAv6OkrGRId92P/DBK+ULdIA=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "Home", "_s", "categories", "setCategories", "products", "setProducts", "id", "img", "name", "count", "category", "price", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "style", "backgroundImage", "product", "toFixed", "viewBox", "xmlns", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/KyoPalWebsite - Copy (2)/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home = () => {\n  const [categories, setCategories] = useState([]);\n  const [products, setProducts] = useState([]);\n\n  // Sample data - replace with API calls later\n  useEffect(() => {\n    // Sample categories\n    setCategories([\n      { id: 1, img: '/images/categories/1.png', name: 'Figures', count: '25' },\n      { id: 2, img: '/images/categories/2.png', name: 'Clothing', count: '18' },\n      { id: 3, img: '/images/categories/3.png', name: 'Accessories', count: '32' },\n      { id: 4, img: '/images/categories/4.png', name: 'Manga', count: '45' },\n      { id: 5, img: '/images/categories/5.png', name: 'Posters', count: '12' },\n      { id: 6, img: '/images/categories/6.png', name: 'Keychains', count: '28' },\n      { id: 7, img: '/images/categories/7.png', name: 'Plushies', count: '15' },\n      { id: 8, img: '/images/categories/8.png', name: 'Collectibles', count: '22' },\n    ]);\n\n    // Sample products\n    setProducts([\n      { id: 1, img: '/images/website/Kyo2.jpg', category: 'Figures', name: 'Anime Figure Collection', price: 49.99 },\n      { id: 2, img: '/images/website/Kyo2.jpg', category: 'Figures', name: 'Premium Statue', price: 89.99 },\n      { id: 3, img: '/images/website/Kyo2.jpg', category: 'Clothing', name: 'Anime T-Shirt', price: 24.99 },\n      { id: 4, img: '/images/website/Kyo2.jpg', category: 'Accessories', name: 'Character Keychain', price: 12.99 },\n      { id: 5, img: '/images/website/Kyo2.jpg', category: 'Manga', name: 'Popular Manga Series', price: 15.99 },\n      { id: 6, img: '/images/website/Kyo2.jpg', category: 'Plushies', name: 'Cute Character Plush', price: 29.99 },\n      { id: 7, img: '/images/website/Kyo2.jpg', category: 'Posters', name: 'Anime Wall Poster', price: 19.99 },\n      { id: 8, img: '/images/website/Kyo2.jpg', category: 'Collectibles', name: 'Limited Edition Item', price: 99.99 },\n    ]);\n  }, []);\n\n  return (\n    <div className=\"font-merienda min-h-screen flex flex-col\">\n      {/* Hero Section */}\n      <section className=\"relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden\">\n        <img \n          src=\"/images/website/background.jpg\" \n          className=\"absolute inset-0 w-full h-full object-cover opacity-5 z-0\" \n          alt=\"background\" \n        />\n        \n        <div className=\"container relative z-10 text-center md:text-left space-y-6 md:space-y-10\">\n          <h1 className=\"text-red-600 text-md md:text-2xl font-bold\">\n            FIRST ANIME STORE IN PALESTINE!\n          </h1>\n          <p className=\"text-black font-extrabold text-3xl md:text-4xl\">\n            Discover World of <span className=\"text-red-600\">Anime</span> Collectibles\n          </p>\n          <p className=\"text-black/70 text-lg md:w-2/3\">\n            Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.\n          </p>\n          <div className=\"flex flex-wrap justify-center md:justify-start gap-4\">\n            <Link \n              to=\"/products\" \n              className=\"bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md\"\n            >\n              Shop New\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"container w-full text-center pt-12\">\n        <p className=\"text-3xl font-extrabold\">Shop by Category</p>\n        <p className=\"text-gray-700 mt-4\">Explore our wide range of anime merchandise categories</p>\n        <section className=\"mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4\">\n          {categories.map((category) => (\n            <article \n              key={category.id}\n              className=\"bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 cursor-pointer hover:scale-105 transition-transform\"\n              style={{ backgroundImage: `url('${category.img}')` }}\n            >\n              <div className=\"relative top-20\">\n                <p className=\"text-lg font-extrabold text-white\">\n                  {category.name || 'Category Name'}\n                </p>\n                <p className=\"text-sm text-white mt-2\">\n                  {category.count || '0'} products\n                </p>\n              </div>\n            </article>\n          ))}\n        </section>\n      </section>\n\n      {/* Featured Products Section */}\n      <section className=\"container pt-12\">\n        <div className=\"text-center space-y-2\">\n          <p className=\"inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm\">\n            Trending Now\n          </p>\n          <p className=\"text-3xl font-extrabold text-gray-800\">\n            Featured Products\n          </p>\n        </div>\n\n        <section className=\"mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <article\n              key={product.id}\n              className=\"group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto\"\n            >\n              <div className=\"overflow-hidden\">\n                <img \n                  src={product.img} \n                  alt=\"Product Image\"\n                  className=\"object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105\"\n                />\n              </div>\n              <div className=\"p-4 flex flex-col justify-between flex-1\">\n                <div>\n                  <p className=\"text-xs text-red-500 uppercase font-semibold\">{product.category}</p>\n                  <p className=\"text-sm font-bold text-gray-800 truncate\">{product.name}</p>\n                  <p className=\"text-sm text-gray-600\">\n                    ${product.price.toFixed(2)} USD\n                  </p>\n                </div>\n                <button className=\"mt-4 inline-block text-center bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200\">\n                  Add to Cart\n                </button>\n              </div>\n            </article>\n          ))}\n        </section>\n        \n        <div className=\"flex items-center justify-center mt-12\">\n          <Link \n            to=\"/products\" \n            className=\"flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-300 hover:bg-red-50 transition-transform duration-300 ease-in-out\"\n          >\n            View All Products\n            <svg className=\"w-5 h-5 ml-2\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\">\n              <path stroke=\"#000000\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"m19 12-6-6m6 6-6 6m6-6H5\"/>\n            </svg>\n          </Link>\n        </div>\n      </section>\n\n      {/* Special Offers Section */}\n      <section className=\"py-12 bg-red-600 text-white mt-20\">\n        <div className=\"container\">\n          <div className=\"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\">\n            <div className=\"space-y-4\">\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">Special Offers</h2>\n              <p className=\"max-w-[600px] md:text-xl\">\n                Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!\n              </p>\n              <div className=\"flex flex-col gap-2 min-[400px]:flex-row\">\n                <Link to=\"/products?sale=true\" className=\"inline-block\">\n                  <button className=\"w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]\">\n                    Shop Sale\n                  </button>\n                </Link>\n              </div>\n            </div>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">20% OFF</h3>\n                  <p className=\"text-sm\">On selected figures</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">FREE SHIPPING</h3>\n                  <p className=\"text-sm\">On orders over $50</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">GUARANTEE</h3>\n                  <p className=\"text-sm\">30-day money back</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">REWARDS</h3>\n                  <p className=\"text-sm\">Earn points with every purchase</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd;IACAO,aAAa,CAAC,CACZ;MAAEG,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAK,CAAC,EACxE;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAK,CAAC,EACzE;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC5E;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAK,CAAC,EACtE;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAK,CAAC,EACxE;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC1E;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAK,CAAC,EACzE;MAAEH,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAK,CAAC,CAC9E,CAAC;;IAEF;IACAJ,WAAW,CAAC,CACV;MAAEC,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,SAAS;MAAEF,IAAI,EAAE,yBAAyB;MAAEG,KAAK,EAAE;IAAM,CAAC,EAC9G;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,SAAS;MAAEF,IAAI,EAAE,gBAAgB;MAAEG,KAAK,EAAE;IAAM,CAAC,EACrG;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,UAAU;MAAEF,IAAI,EAAE,eAAe;MAAEG,KAAK,EAAE;IAAM,CAAC,EACrG;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,aAAa;MAAEF,IAAI,EAAE,oBAAoB;MAAEG,KAAK,EAAE;IAAM,CAAC,EAC7G;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,OAAO;MAAEF,IAAI,EAAE,sBAAsB;MAAEG,KAAK,EAAE;IAAM,CAAC,EACzG;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,UAAU;MAAEF,IAAI,EAAE,sBAAsB;MAAEG,KAAK,EAAE;IAAM,CAAC,EAC5G;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,SAAS;MAAEF,IAAI,EAAE,mBAAmB;MAAEG,KAAK,EAAE;IAAM,CAAC,EACxG;MAAEL,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,0BAA0B;MAAEG,QAAQ,EAAE,cAAc;MAAEF,IAAI,EAAE,sBAAsB;MAAEG,KAAK,EAAE;IAAM,CAAC,CACjH,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA;IAAKa,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAEvDd,OAAA;MAASa,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC/Ed,OAAA;QACEe,GAAG,EAAC,gCAAgC;QACpCF,SAAS,EAAC,2DAA2D;QACrEG,GAAG,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEFpB,OAAA;QAAKa,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACvFd,OAAA;UAAIa,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAE3D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGa,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,oBAC1C,eAAAd,OAAA;YAAMa,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,iBAC/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpB,OAAA;UAAGa,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAE9C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpB,OAAA;UAAKa,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnEd,OAAA,CAACF,IAAI;YACHuB,EAAE,EAAC,WAAW;YACdR,SAAS,EAAC,8GAA8G;YAAAC,QAAA,EACzH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpB,OAAA;MAASa,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACrDd,OAAA;QAAGa,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC3DpB,OAAA;QAAGa,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAsD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5FpB,OAAA;QAASa,SAAS,EAAC,+EAA+E;QAAAC,QAAA,EAC/FX,UAAU,CAACmB,GAAG,CAAEX,QAAQ,iBACvBX,OAAA;UAEEa,SAAS,EAAC,mJAAmJ;UAC7JU,KAAK,EAAE;YAAEC,eAAe,EAAE,QAAQb,QAAQ,CAACH,GAAG;UAAK,CAAE;UAAAM,QAAA,eAErDd,OAAA;YAAKa,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9Bd,OAAA;cAAGa,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC7CH,QAAQ,CAACF,IAAI,IAAI;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACJpB,OAAA;cAAGa,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GACnCH,QAAQ,CAACD,KAAK,IAAI,GAAG,EAAC,WACzB;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAXDT,QAAQ,CAACJ,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYT,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGVpB,OAAA;MAASa,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAClCd,OAAA;QAAKa,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCd,OAAA;UAAGa,SAAS,EAAC,2FAA2F;UAAAC,QAAA,EAAC;QAEzG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpB,OAAA;UAAGa,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpB,OAAA;QAASa,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EAC3ET,QAAQ,CAACiB,GAAG,CAAEG,OAAO,iBACpBzB,OAAA;UAEEa,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAExGd,OAAA;YAAKa,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9Bd,OAAA;cACEe,GAAG,EAAEU,OAAO,CAACjB,GAAI;cACjBQ,GAAG,EAAC,eAAe;cACnBH,SAAS,EAAC;YAAuG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpB,OAAA;YAAKa,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDd,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAGa,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAEW,OAAO,CAACd;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFpB,OAAA;gBAAGa,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEW,OAAO,CAAChB;cAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EpB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAClC,EAACW,OAAO,CAACb,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC,EAAC,MAC7B;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpB,OAAA;cAAQa,SAAS,EAAC,6HAA6H;cAAAC,QAAA,EAAC;YAEhJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GArBDK,OAAO,CAAClB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBR,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEVpB,OAAA;QAAKa,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDd,OAAA,CAACF,IAAI;UACHuB,EAAE,EAAC,WAAW;UACdR,SAAS,EAAC,mKAAmK;UAAAC,QAAA,GAC9K,mBAEC,eAAAd,OAAA;YAAKa,SAAS,EAAC,cAAc;YAACc,OAAO,EAAC,WAAW;YAACC,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAAAf,QAAA,eAC9Fd,OAAA;cAAM8B,MAAM,EAAC,SAAS;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAA0B;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpB,OAAA;MAASa,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eACpDd,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBd,OAAA;UAAKa,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/Dd,OAAA;YAAKa,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBd,OAAA;cAAIa,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/FpB,OAAA;cAAGa,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpB,OAAA;cAAKa,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvDd,OAAA,CAACF,IAAI;gBAACuB,EAAE,EAAC,qBAAqB;gBAACR,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACrDd,OAAA;kBAAQa,SAAS,EAAC,6GAA6G;kBAAAC,QAAA,EAAC;gBAEhI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpB,OAAA;YAAKa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCd,OAAA;cAAKa,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDd,OAAA;gBAAKa,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEd,OAAA;kBAAKa,SAAS,EAAC,gBAAgB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACH,OAAO,EAAC,WAAW;kBAACC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,eACtHd,OAAA;oBAAM+B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAA4I;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtN,CAAC,eACNpB,OAAA;kBAAIa,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtCpB,OAAA;kBAAGa,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKa,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDd,OAAA;gBAAKa,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEd,OAAA;kBAAKa,SAAS,EAAC,gBAAgB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACH,OAAO,EAAC,WAAW;kBAACC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,gBACtHd,OAAA;oBAAMkC,CAAC,EAAC;kBAAiE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjFpB,OAAA;oBAAM+B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAyP;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnU,CAAC,eACNpB,OAAA;kBAAIa,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5CpB,OAAA;kBAAGa,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKa,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDd,OAAA;gBAAKa,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEd,OAAA;kBAAKa,SAAS,EAAC,gBAAgB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACH,OAAO,EAAC,WAAW;kBAACC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,eACtHd,OAAA;oBAAM+B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAgM;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1Q,CAAC,eACNpB,OAAA;kBAAIa,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCpB,OAAA;kBAAGa,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKa,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDd,OAAA;gBAAKa,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEd,OAAA;kBAAKa,SAAS,EAAC,gBAAgB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACH,OAAO,EAAC,WAAW;kBAACC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,eACtHd,OAAA;oBAAM+B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAyW;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnb,CAAC,eACNpB,OAAA;kBAAIa,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtCpB,OAAA;kBAAGa,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClB,EAAA,CAxMID,IAAI;AAAAkC,EAAA,GAAJlC,IAAI;AA0MV,eAAeA,IAAI;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}