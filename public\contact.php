<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
$title = "Contact";
include_once(__DIR__ . '/../includes/store/header.php');

$settings = mysqli_fetch_assoc(mysqli_query($mysqli, "SELECT * FROM StoreSettings LIMIT 1"));
?>
<section class="container">
    <header class="text-center p-10">
        <h1 class="text-3xl font-bold">Contact Us</h1>
        <p class="text-gray-500 mt-2">
            Have questions about our products or need assistance? We're here to help! Reach out to us using any of the
            methods below.
        </p>
    </header>
    <div class="grid md:grid-cols-2 gap-8 mb-10">
        <section class="bg-white rounded-lg shadow-lg p-6 space-y-6 border border-red-200 hover:shadow-xl transition duration-300">
            <form aria-label="Contact Form" class="space-y-6">
                <div>
                    <h2 class="text-xl font-semibold mb-2">Send us a message</h2>
                    <p class="text-sm text-gray-500">Fill out the form below and we'll get back to you as soon as possible.</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label for="first-name" class="block text-sm font-medium text-gray-700">First name</label>
                        <input type="text" id="first-name" name="first-name" placeholder="John" required
                            class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-red-500 focus:ring-2" />
                    </div>
                    <div>
                        <label for="last-name" class="block text-sm font-medium text-gray-700">Last name</label>
                        <input type="text" id="last-name" name="last-name" placeholder="Doe" required
                            class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-red-500 focus:ring-2" />
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required
                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-red-500 focus:ring-2" />
                </div>

                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                    <input type="text" id="subject" name="subject" placeholder="How can we help you?"
                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-red-500 focus:ring-2" />
                </div>

                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                    <textarea id="message" name="message" rows="4" placeholder="Please provide as much detail as possible..."
                        required
                        class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-red-500 focus:ring-2"></textarea>
                </div>

                <div>
                    <button type="submit"
                        class="bg-red-600 text-white w-full py-2 px-6 rounded-md hover:bg-red-700 transition">Send Message</button>
                </div>
            </form>
        </section>

        <section class="space-y-6">
            <div class="bg-white rounded-lg shadow-lg p-6 not-italic space-y-4 border border-red-200 hover:shadow-xl transition duration-300" data-v0-t="card">
                <div class="space-y-6">
                    <div class="flex items-start gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-6 w-6 text-red-600 flex-shrink-0">
                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        <div>
                            <h3 class="font-semibold">Our Location</h3>
                            <p class="text-muted-foreground"><?php echo htmlspecialchars($settings['location'] ?? ''); ?></p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-6 w-6 text-red-600 flex-shrink-0">
                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                        </svg>
                        <div>
                            <h3 class="font-semibold">Phone</h3>
                            <p class="text-muted-foreground"><?php echo htmlspecialchars($settings['phone_number'] ?? ''); ?></p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-6 w-6 text-red-600 flex-shrink-0">
                            <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                        </svg>
                        <div>
                            <h3 class="font-semibold">Email</h3>
                            <p class="text-muted-foreground"><?php echo htmlspecialchars($settings['email'] ?? ''); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <article class="bg-white rounded-lg shadow-lg p-6 space-y-4 border border-red-200 hover:shadow-xl transition duration-300">
                <h2 class="text-lg font-semibold">Frequently Asked Questions</h2>
                <div>
                    <h3 class="font-medium">How long does shipping take?</h3>
                    <p class="text-sm text-gray-600">Standard shipping typically takes 3–5 business days within the country
                        and 7–14 business days for international orders.</p>
                </div>
                <div>
                    <h3 class="font-medium">Do you offer returns?</h3>
                    <p class="text-sm text-gray-600">Yes, we offer a 30-day return policy for most items. Please check our
                        Returns Policy for more details.</p>
                </div>
                <div>
                    <h3 class="font-medium">Are the products authentic?</h3>
                    <p class="text-sm text-gray-600">Yes, all our products are 100% authentic and sourced directly from
                        official manufacturers or authorized distributors.</p>
                </div>
            </article>
        </section>
    </div>
</section>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>