<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getCategory($_GET['id']);
        } else {
            getCategories();
        }
        break;
    case 'POST':
        createCategory();
        break;
    case 'PUT':
        if (isset($_GET['id'])) {
            updateCategory($_GET['id']);
        } else {
            sendResponse(['error' => 'Category ID required'], 400);
        }
        break;
    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteCategory($_GET['id']);
        } else {
            sendResponse(['error' => 'Category ID required'], 400);
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getCategories() {
    global $mysqli;

    $query = "
        SELECT c.*, COUNT(p.id) as product_count
        FROM Categories c
        LEFT JOIN Products p ON c.id = p.category_id
        GROUP BY c.id
        ORDER BY c.name
    ";
    $categories = getAllRows($query);

    foreach ($categories as &$category) {
        $category['product_count'] = (int)$category['product_count'];
    }

    sendResponse(['categories' => $categories]);
}

function getCategory($id) {
    global $mysqli;

    $id = (int)$id;
    $query = "
        SELECT c.*, COUNT(p.id) as product_count
        FROM Categories c
        LEFT JOIN Products p ON c.id = p.category_id
        WHERE c.id = $id
        GROUP BY c.id
    ";
    $category = getSingleRow($query);

    if (!$category) {
        sendResponse(['error' => 'Category not found'], 404);
    }

    $category['product_count'] = (int)$category['product_count'];

    sendResponse(['category' => $category]);
}

function createCategory() {
    requireAdmin();

    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['name']);

    global $mysqli;

    $name = escapeString($data['name']);

    // Check if category name already exists
    $checkQuery = "SELECT id FROM Categories WHERE name = '$name'";
    $existing = getSingleRow($checkQuery);
    if ($existing) {
        sendResponse(['error' => 'Category name already exists'], 400);
    }

    $imagePath = isset($data['image_path']) ? "'" . escapeString($data['image_path']) . "'" : 'NULL';
    $insertQuery = "INSERT INTO Categories (name, image_path) VALUES ('$name', $imagePath)";
    executeQuery($insertQuery);

    $categoryId = mysqli_insert_id($mysqli);

    sendResponse(['message' => 'Category created successfully', 'id' => $categoryId], 201);
}

function updateCategory($id) {
    requireAdmin();

    $data = json_decode(file_get_contents('php://input'), true);

    global $mysqli;

    $id = (int)$id;

    // Check if category exists
    $checkQuery = "SELECT id FROM Categories WHERE id = $id";
    $existing = getSingleRow($checkQuery);
    if (!$existing) {
        sendResponse(['error' => 'Category not found'], 404);
    }

    $updates = [];

    if (isset($data['name'])) {
        $name = escapeString($data['name']);

        // Check if new name already exists (excluding current category)
        $nameCheckQuery = "SELECT id FROM Categories WHERE name = '$name' AND id != $id";
        $nameExists = getSingleRow($nameCheckQuery);
        if ($nameExists) {
            sendResponse(['error' => 'Category name already exists'], 400);
        }

        $updates[] = "name = '$name'";
    }

    if (isset($data['image_path'])) {
        $imagePath = escapeString($data['image_path']);
        $updates[] = "image_path = '$imagePath'";
    }

    if (!empty($updates)) {
        $updates[] = "updated_at = CURRENT_TIMESTAMP";
        $updateQuery = "UPDATE Categories SET " . implode(', ', $updates) . " WHERE id = $id";
        executeQuery($updateQuery);
    }

    sendResponse(['message' => 'Category updated successfully']);
}

function deleteCategory($id) {
    requireAdmin();

    global $mysqli;

    $id = (int)$id;

    // Check if category has products
    $countQuery = "SELECT COUNT(*) as count FROM Products WHERE category_id = $id";
    $countResult = getSingleRow($countQuery);
    $count = $countResult['count'];

    if ($count > 0) {
        sendResponse(['error' => 'Cannot delete category with products'], 400);
    }

    // Check if category exists
    $checkQuery = "SELECT id FROM Categories WHERE id = $id";
    $existing = getSingleRow($checkQuery);
    if (!$existing) {
        sendResponse(['error' => 'Category not found'], 404);
    }

    $deleteQuery = "DELETE FROM Categories WHERE id = $id";
    executeQuery($deleteQuery);

    sendResponse(['message' => 'Category deleted successfully']);
}
