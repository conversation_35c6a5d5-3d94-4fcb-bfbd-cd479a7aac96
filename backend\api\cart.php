<?php
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getCart();
        break;
    case 'POST':
        addToCart();
        break;
    case 'PUT':
        updateCartItem();
        break;
    case 'DELETE':
        if (isset($_GET['item_id'])) {
            removeFromCart($_GET['item_id']);
        } else {
            clearCart();
        }
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

function getCart() {
    $user = getCurrentUser();
    global $mysqli;

    $userId = $user['id'];

    // Get or create cart order
    $orderQuery = "SELECT id FROM Orders WHERE user_id = $userId AND status = 'cart'";
    $order = getSingleRow($orderQuery);

    if (!$order) {
        sendResponse(['cart' => [], 'total' => 0, 'count' => 0]);
    }

    $orderId = $order['id'];

    // Get cart items
    $itemsQuery = "
        SELECT oi.*, p.name, p.description, pi.image_path,
               (oi.quantity * oi.price) as subtotal
        FROM OrderItems oi
        JOIN Products p ON oi.product_id = p.id
        LEFT JOIN ProductImages pi ON p.id = pi.product_id AND pi.is_primary = 1
        WHERE oi.order_id = $orderId
        GROUP BY oi.id
    ";
    $items = getAllRows($itemsQuery);

    $total = 0;
    foreach ($items as &$item) {
        $item['price'] = (float)$item['price'];
        $item['subtotal'] = (float)$item['subtotal'];
        $total += $item['subtotal'];
    }

    sendResponse([
        'cart' => $items,
        'total' => $total,
        'count' => count($items)
    ]);
}

function addToCart() {
    $user = getCurrentUser();
    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['product_id', 'quantity']);

    global $mysqli;

    $productId = (int)$data['product_id'];
    $quantity = (int)$data['quantity'];
    $userId = $user['id'];

    // Get product details
    $productQuery = "SELECT * FROM Products WHERE id = $productId";
    $product = getSingleRow($productQuery);

    if (!$product) {
        sendResponse(['error' => 'Product not found'], 404);
    }

    // Check stock
    if ($product['stock'] < $quantity) {
        sendResponse(['error' => 'Insufficient stock'], 400);
    }

    // Get or create cart order
    $orderQuery = "SELECT id FROM Orders WHERE user_id = $userId AND status = 'cart'";
    $order = getSingleRow($orderQuery);

    if (!$order) {
        $createOrderQuery = "INSERT INTO Orders (user_id, status) VALUES ($userId, 'cart')";
        executeQuery($createOrderQuery);
        $orderId = mysqli_insert_id($mysqli);
    } else {
        $orderId = $order['id'];
    }

    // Check if item already in cart
    $existingQuery = "SELECT * FROM OrderItems WHERE order_id = $orderId AND product_id = $productId";
    $existingItem = getSingleRow($existingQuery);

    if ($existingItem) {
        // Update quantity
        $newQuantity = $existingItem['quantity'] + $quantity;
        $updateQuery = "UPDATE OrderItems SET quantity = $newQuantity WHERE id = {$existingItem['id']}";
        executeQuery($updateQuery);
    } else {
        // Add new item
        $price = $product['price'];
        $insertQuery = "INSERT INTO OrderItems (order_id, product_id, quantity, price) VALUES ($orderId, $productId, $quantity, $price)";
        executeQuery($insertQuery);
    }

    sendResponse(['message' => 'Item added to cart successfully']);
}

function updateCartItem() {
    $user = getCurrentUser();
    $data = json_decode(file_get_contents('php://input'), true);
    validateRequired($data, ['item_id', 'quantity']);

    global $mysqli;

    $itemId = (int)$data['item_id'];
    $quantity = (int)$data['quantity'];
    $userId = $user['id'];

    // Verify item belongs to user's cart
    $verifyQuery = "
        SELECT oi.* FROM OrderItems oi
        JOIN Orders o ON oi.order_id = o.id
        WHERE oi.id = $itemId AND o.user_id = $userId AND o.status = 'cart'
    ";
    $item = getSingleRow($verifyQuery);

    if (!$item) {
        sendResponse(['error' => 'Cart item not found'], 404);
    }

    if ($quantity <= 0) {
        // Remove item
        $deleteQuery = "DELETE FROM OrderItems WHERE id = $itemId";
        executeQuery($deleteQuery);
    } else {
        // Update quantity
        $updateQuery = "UPDATE OrderItems SET quantity = $quantity WHERE id = $itemId";
        executeQuery($updateQuery);
    }

    sendResponse(['message' => 'Cart updated successfully']);
}

function removeFromCart($itemId) {
    $user = getCurrentUser();
    global $mysqli;

    $itemId = (int)$itemId;
    $userId = $user['id'];

    // Verify item belongs to user's cart
    $verifyQuery = "
        SELECT oi.* FROM OrderItems oi
        JOIN Orders o ON oi.order_id = o.id
        WHERE oi.id = $itemId AND o.user_id = $userId AND o.status = 'cart'
    ";
    $item = getSingleRow($verifyQuery);

    if (!$item) {
        sendResponse(['error' => 'Cart item not found'], 404);
    }

    $deleteQuery = "DELETE FROM OrderItems WHERE id = $itemId";
    executeQuery($deleteQuery);

    sendResponse(['message' => 'Item removed from cart']);
}

function clearCart() {
    $user = getCurrentUser();
    global $mysqli;

    $userId = $user['id'];

    $clearQuery = "
        DELETE oi FROM OrderItems oi
        JOIN Orders o ON oi.order_id = o.id
        WHERE o.user_id = $userId AND o.status = 'cart'
    ";
    executeQuery($clearQuery);

    sendResponse(['message' => 'Cart cleared successfully']);
}
