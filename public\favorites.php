<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');
if (!isset($_SESSION['user'])) {
    header('Location: /login.php');
    exit;
}
include_once(__DIR__ . '/../includes/store/header.php');
$title = "Favorites";
$favoriteProducts = [
    ['id' => 1, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Figures', 'name' => 'Anime Figure A', 'price' => 49.99],
    ['id' => 2, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Manga', 'name' => 'Manga Volume B', 'price' => 19.99],
    ['id' => 3, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Accessories', 'name' => 'Anime Keychain C', 'price' => 12.99],
    ['id' => 4, 'img' => 'assets/images/Kyo2.jpg', 'category' => 'Clothing', 'name' => 'T-shirt D', 'price' => 29.99],
];
?>

<section class="container py-10">
    <div class="flex flex-col space-y-6">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800">My Favorites</h1>
            <p class="text-gray-400 mt-2">Items you've saved to your wishlist</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <?php include_once(__DIR__ . '/../../includes/store/components/sidebar_profile.php'); ?>

            <div class="md:col-span-3">
                <?php if (empty($favoriteProducts)): ?>
                    <div class="bg-white rounded-lg shadow-md p-10 text-center">
                        <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                            </path>
                        </svg>
                        <h2 class="mt-4 text-lg font-medium">No favorites yet</h2>
                        <p class="mt-2 text-gray-500">Browse our products and add some to your favorites</p>
                        <a href="/products.php"
                            class="mt-4 inline-block bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition">
                            Browse Products
                        </a>
                    </div>
                <?php else: ?>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
                        <div class="p-6 border-b border-gray-100">
                            <h2 class="text-xl font-bold text-gray-800">Saved Items</h2>
                        </div>

                        <div class="p-6">
                            <div class="space-y-4">
                                <?php foreach ($favoriteProducts as $product): ?>
                                    <div
                                        class="flex flex-row items-center border border-gray-100 rounded-lg p-4 hover:shadow-md transition-all duration-300 gap-4">
                                        <div class="w-20 h-20 relative flex-shrink-0">
                                            <img src="<?= $product['img'] ?>" alt="<?= $product['name'] ?>"
                                                class="w-full h-full object-cover rounded-md">
                                        </div>

                                        <div class="flex-1 text-center sm:text-left">
                                            <span
                                                class="inline-block px-2 py-1 bg-red-50 text-red-600 text-xs rounded-full mb-2"><?= $product['category'] ?></span>
                                            <h3 class="font-bold text-gray-800"><?= $product['name'] ?></h3>
                                            <p class="text-gray-500 mt-1">$<?= number_format($product['price'], 2) ?></p>
                                        </div>

                                        <div class="flex items-center gap-3 w-auto">
                                            <a href="/product-details.php?id=<?= $product['id'] ?>"
                                                class="inline-block text-center border border-gray-200 text-gray-600 py-2 px-4 rounded font-medium hover:bg-gray-50 transition-colors duration-200">
                                                View Details
                                            </a>
                                            <button type="button"
                                                class="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-200 cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="p-6 bg-gray-50 border-t border-gray-100 flex justify-end">
                            <a href="/products.php" class="text-red-600 hover:text-red-700 font-medium">
                                Browse more products →
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>