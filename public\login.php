<?php
session_start();
include_once(__DIR__ . '/../includes/database.php');

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    if ($email && $password) {
        $stmt = mysqli_prepare($mysqli, "SELECT id, name, email, password, role FROM Users WHERE email = ? LIMIT 1");
        mysqli_stmt_bind_param($stmt, 's', $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role']
            ];
            // Cart sync logic
            if (!empty($_POST['cart_data'])) {
                $cartData = json_decode($_POST['cart_data'], true);
                if ($cartData && is_array($cartData)) {
                    // Find or create a cart order for this user
                    $orderRes = mysqli_query($mysqli, "SELECT id FROM Orders WHERE user_id = {$user['id']} AND status = 'cart' LIMIT 1");
                    if ($order = mysqli_fetch_assoc($orderRes)) {
                        $orderId = $order['id'];
                    } else {
                        mysqli_query($mysqli, "INSERT INTO Orders (user_id, status, total_price) VALUES ({$user['id']}, 'cart', 0)");
                        $orderId = mysqli_insert_id($mysqli);
                    }
                    // Insert items
                    foreach ($cartData as $item) {
                        $pid = (int)($item['id'] ?? 0);
                        $qty = (int)($item['quantity'] ?? 1);
                        $price = (float)($item['price'] ?? 0);
                        if ($pid > 0 && $qty > 0) {
                            // Check if item already exists
                            $exists = mysqli_query($mysqli, "SELECT id FROM OrderItems WHERE order_id = $orderId AND product_id = $pid");
                            if (mysqli_num_rows($exists)) {
                                mysqli_query($mysqli, "UPDATE OrderItems SET quantity = quantity + $qty WHERE order_id = $orderId AND product_id = $pid");
                            } else {
                                mysqli_query($mysqli, "INSERT INTO OrderItems (order_id, product_id, quantity, price) VALUES ($orderId, $pid, $qty, $price)");
                            }
                        }
                    }
                }
            }
            header('Location: /');
            exit;
        } else {
            $error = 'Invalid email or password.';
        }
    } else {
        $error = 'Please enter both email and password.';
    }
}

$title = "Login";
include_once(__DIR__ . '/../includes/store/header.php');
?>
<section class="container flex items-center justify-center py-16">
    <?php if (!empty(
        $error)): ?>
        <div class="bg-red-100 text-red-700 px-4 py-2 rounded mb-4 text-center"><?= htmlspecialchars($error) ?></div>
    <?php endif; ?>
    <form id="login-form" class="bg-white rounded-lg shadow-lg p-6 space-y-4 border border-red-200 hover:shadow-xl transition duration-300 w-full max-w-md" aria-label="Login Form" method="POST" action="login.php" onsubmit="return validateForm()">
        <div class="text-center space-y-2">
            <img src="<?= "images/" ?>website/logo.png" height="70" width="70" alt=""  class="mx-auto">
            <h2 class="text-xl font-semibold">Welcome Back</h2>
            <p class="text-sm text-gray-500">Enter your credentials to access your account.</p>
        </div>

        <div>
            <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
            <input type="text" id="username" name="username" placeholder="Enter your username" 
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
            <p id="username-error" class="text-red-500 text-sm hidden">Username is required</p>
        </div>

        <div>
            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
            <input type="password" id="password" name="password" placeholder="Enter your password" 
                class="mt-1 w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-gray-400" />
            <p id="password-error" class="text-red-500 text-sm hidden">Password is required</p>
        </div>

        <div class="flex items-center justify-between space-x-4">
            <div class="flex items-center">
                <input type="checkbox" id="remember-me" name="remember-me" class="h-4 w-4 accent-red-600" />
                <label for="remember-me" class="text-sm text-gray-500 ml-2">Remember me</label>
            </div>
            <div>
                <a href="/forgot_password.php" class="text-sm text-red-600 hover:text-red-700">Forgot Password?</a>
            </div>
        </div>

        <div>
            <button type="submit"
                class="bg-red-600 text-white w-full py-2 px-6 rounded-md hover:bg-red-700 transition">Login</button>
        </div>
        <div class="text-center">
            <p class="text-sm text-gray-500">Don't have an account?
                <a href="/register.php" class="text-red-600 hover:underline">Register</a>
            </p>
        </div>
    </form>
</section>
<script>
    function validateForm() {
        document.getElementById('username-error').classList.add('hidden');
        document.getElementById('password-error').classList.add('hidden');

        let username = document.getElementById('username').value;
        let password = document.getElementById('password').value;

        let valid = true;

        if (username.trim() === "") {
            document.getElementById('username-error').classList.remove('hidden');
            valid = false;
        }

        if (password.trim() === "") {
            document.getElementById('password-error').classList.remove('hidden');
            valid = false;
        }

        return valid;
    }

    // On login form submit, if cart exists in localStorage, add it to a hidden field
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            var cart = localStorage.getItem('cart');
            if (cart) {
                let input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'cart_data';
                input.value = cart;
                loginForm.appendChild(input);
            }
        });
    }
</script>
<?php include_once(__DIR__ . '/../includes/store/footer.php'); ?>